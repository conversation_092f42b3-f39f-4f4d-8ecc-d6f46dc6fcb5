#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
策略就绪验证脚本
快速验证策略是否可以在VNPY中使用
"""

import os
import sys
from pathlib import Path

def main():
    print("🚀 VNPY策略就绪验证")
    print("=" * 50)
    
    success_count = 0
    total_checks = 6
    
    # 检查1: 策略文件存在
    print("1. 检查策略文件...")
    current_dir = Path(__file__).parent
    strategy_file = current_dir / "signal_system_strategy.py"
    
    if strategy_file.exists():
        print("   ✅ SignalSystemStrategy 文件存在")
        success_count += 1
    else:
        print("   ❌ 策略文件不存在")
    
    # 检查2: __init__.py配置
    print("2. 检查策略注册...")
    init_file = current_dir / "__init__.py"
    
    if init_file.exists():
        with open(init_file, 'r', encoding='utf-8') as f:
            content = f.read()
        if 'SignalSystemStrategy' in content and '__all__' in content:
            print("   ✅ 策略已注册到__init__.py")
            success_count += 1
        else:
            print("   ❌ 策略未正确注册")
    else:
        print("   ❌ __init__.py文件不存在")
    
    # 检查3: 基础依赖
    print("3. 检查基础依赖...")
    try:
        import numpy
        print("   ✅ numpy可用")
        success_count += 1
    except ImportError:
        print("   ❌ numpy不可用")
    
    # 检查4: PyTorch (可选)
    print("4. 检查PyTorch...")
    try:
        import torch
        if torch.cuda.is_available():
            print(f"   ✅ PyTorch + CUDA可用 ({torch.cuda.get_device_name(0)})")
        else:
            print("   ✅ PyTorch可用 (CPU模式)")
        success_count += 1
    except ImportError:
        print("   ⚠️  PyTorch不可用 (策略仍可运行)")
        success_count += 0.5  # 部分成功
    
    # 检查5: Signal System模块
    print("5. 检查Signal System...")
    signal_dir = current_dir / "signal_system"
    if signal_dir.exists():
        dll_file = signal_dir / "signal_system.dll"
        if dll_file.exists():
            print("   ✅ Signal System模块完整")
            success_count += 1
        else:
            print("   ⚠️  Signal System部分文件缺失")
            success_count += 0.5
    else:
        print("   ⚠️  Signal System目录不存在")
        success_count += 0.5
    
    # 检查6: 策略导入测试
    print("6. 测试策略导入...")
    try:
        # 添加当前目录到路径
        sys.path.insert(0, str(current_dir))
        
        # 尝试导入策略 (不依赖VNPY)
        spec = __import__('importlib.util', fromlist=['spec_from_file_location']).spec_from_file_location(
            "signal_system_strategy", strategy_file
        )
        module = __import__('importlib.util', fromlist=['module_from_spec']).module_from_spec(spec)
        
        print("   ✅ 策略文件可以正常导入")
        success_count += 1
    except Exception as e:
        print(f"   ❌ 策略导入失败: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("验证结果")
    print("=" * 50)
    
    success_rate = success_count / total_checks * 100
    print(f"就绪度: {success_count:.1f}/{total_checks} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 策略已就绪！")
        print("\n📋 下一步操作:")
        print("1. 重启VeighNa Studio")
        print("2. 进入CTA策略模块")
        print("3. 查找 'SignalSystemStrategy'")
        print("4. 配置参数并启动策略")
        
        print("\n⚙️ 推荐配置:")
        print("- 交易品种: rb2501.SHFE")
        print("- confidence_threshold: 0.6")
        print("- position_size: 1")
        print("- stop_loss_pct: 0.02")
        print("- take_profit_pct: 0.04")
        
    elif success_rate >= 60:
        print("⚠️  策略基本可用，但建议完善环境")
        print("\n🔧 建议操作:")
        if success_count < 3:
            print("- 安装缺失的依赖包")
        if success_count < 5:
            print("- 检查Signal System文件完整性")
        print("- 重启VeighNa Studio后尝试")
        
    else:
        print("❌ 策略环境存在问题")
        print("\n🛠️  需要修复:")
        print("- 检查文件完整性")
        print("- 安装必要依赖")
        print("- 重新部署策略文件")
    
    print(f"\n📖 详细说明请查看: VNPY_STRATEGY_SOLUTION.md")
    print("🚀 祝你交易成功！")

if __name__ == "__main__":
    main()
