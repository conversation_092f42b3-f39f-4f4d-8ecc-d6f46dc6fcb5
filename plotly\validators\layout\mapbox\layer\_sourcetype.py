import _plotly_utils.basevalidators


class SourcetypeValidator(_plotly_utils.basevalidators.EnumeratedValidator):
    def __init__(
        self, plotly_name="sourcetype", parent_name="layout.mapbox.layer", **kwargs
    ):
        super(SourcetypeValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "plot"),
            values=kwargs.pop("values", ["geojson", "vector", "raster", "image"]),
            **kwargs,
        )
