#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Signal System深度学习策略测试脚本
验证策略的各个组件是否正常工作
"""

import os
import sys
import time
import traceback
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """测试模块导入"""
    print("=" * 60)
    print("1. 模块导入测试")
    print("=" * 60)
    
    results = {}
    
    # 测试基础模块
    try:
        import numpy as np
        results['numpy'] = f"✅ {np.__version__}"
    except ImportError as e:
        results['numpy'] = f"❌ {e}"
    
    # 测试PyTorch
    try:
        import torch
        results['torch'] = f"✅ {torch.__version__}"
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            results['cuda'] = f"✅ {gpu_name}"
        else:
            results['cuda'] = "❌ CUDA不可用"
    except ImportError as e:
        results['torch'] = f"❌ {e}"
        results['cuda'] = "❌ PyTorch未安装"
    
    # 测试VNPY
    try:
        from vnpy_ctastrategy import CtaTemplate
        results['vnpy'] = "✅ 可用"
    except ImportError as e:
        results['vnpy'] = f"❌ {e}"
    
    # 测试Signal System
    try:
        from signal_system.gpu_wrapper import GPUSignalWrapperFixed
        results['signal_system'] = "✅ 可用"
    except ImportError as e:
        results['signal_system'] = f"❌ {e}"
    
    # 输出结果
    for module, status in results.items():
        print(f"  {module:15}: {status}")
    
    return results

def test_strategy_creation():
    """测试策略创建"""
    print("\n" + "=" * 60)
    print("2. 策略创建测试")
    print("=" * 60)
    
    try:
        from signal_system_deep_learning_strategy import SignalSystemDeepLearningStrategy
        
        # 创建策略实例
        strategy = SignalSystemDeepLearningStrategy(
            cta_engine=None,
            strategy_name="test_strategy",
            vt_symbol="rb2501.SHFE",
            setting={
                "confidence_threshold": 0.65,
                "position_size": 1,
                "sequence_length": 20
            }
        )
        
        print("✅ 策略实例创建成功")
        print(f"  策略名称: {strategy.strategy_name}")
        print(f"  交易品种: {strategy.vt_symbol}")
        print(f"  作者: {strategy.author}")
        
        # 测试策略初始化
        strategy.on_init()
        print("✅ 策略初始化成功")
        
        # 测试策略启动
        strategy.on_start()
        print("✅ 策略启动成功")
        
        return strategy
        
    except Exception as e:
        print(f"❌ 策略创建失败: {e}")
        traceback.print_exc()
        return None

def test_deep_learning_model(strategy):
    """测试深度学习模型"""
    print("\n" + "=" * 60)
    print("3. 深度学习模型测试")
    print("=" * 60)
    
    if strategy is None:
        print("❌ 策略未创建，跳过模型测试")
        return
    
    try:
        # 测试模型创建
        if strategy.dl_model.model is not None:
            print("✅ 深度学习模型创建成功")
            print(f"  设备: {strategy.dl_model.device}")
            print(f"  输入维度: {strategy.dl_model.input_size}")
            print(f"  隐藏维度: {strategy.dl_model.hidden_size}")
        else:
            print("❌ 深度学习模型创建失败")
            return
        
        # 测试模型推理
        import numpy as np
        test_features = np.random.randn(20, 6)  # 20个时间步，6个特征
        
        result = strategy.dl_model.predict(test_features)
        print("✅ 模型推理测试成功")
        print(f"  预测动作: {result['action']}")
        print(f"  置信度: {result['confidence']:.3f}")
        print(f"  概率分布: {[f'{p:.3f}' for p in result['probabilities']]}")
        
    except Exception as e:
        print(f"❌ 深度学习模型测试失败: {e}")
        traceback.print_exc()

def test_signal_system(strategy):
    """测试Signal System"""
    print("\n" + "=" * 60)
    print("4. Signal System测试")
    print("=" * 60)
    
    if strategy is None:
        print("❌ 策略未创建，跳过Signal System测试")
        return
    
    try:
        if strategy.signal_system is not None:
            print("✅ Signal System初始化成功")
            
            # 测试信号生成
            test_data = {
                'close': 3500.0,
                'open': 3490.0,
                'high': 3520.0,
                'low': 3480.0,
                'volume': 1000000
            }
            
            result = strategy.signal_system.generate_signal(test_data)
            print("✅ Signal System信号生成测试成功")
            print(f"  信号: {result.get('signal', 'N/A')}")
            print(f"  置信度: {result.get('confidence', 0):.3f}")
            print(f"  设备: {result.get('device', 'N/A')}")
            
        else:
            print("⚠️  Signal System未初始化，使用深度学习模式")
            
    except Exception as e:
        print(f"❌ Signal System测试失败: {e}")
        traceback.print_exc()

def test_technical_analyzer(strategy):
    """测试技术分析器"""
    print("\n" + "=" * 60)
    print("5. 技术分析器测试")
    print("=" * 60)
    
    if strategy is None:
        print("❌ 策略未创建，跳过技术分析器测试")
        return
    
    try:
        # 模拟价格数据
        import numpy as np
        base_price = 3500.0
        
        for i in range(25):  # 添加25个数据点
            price = base_price + np.random.randn() * 10
            volume = 1000000 + np.random.randint(-100000, 100000)
            strategy.technical_analyzer.update(price, volume)
        
        print("✅ 技术分析器数据更新成功")
        print(f"  价格数据点数: {len(strategy.technical_analyzer.price_data)}")
        print(f"  成交量数据点数: {len(strategy.technical_analyzer.volume_data)}")
        
        # 测试特征计算
        features = strategy.technical_analyzer.calculate_features(20)
        if features is not None:
            print("✅ 技术特征计算成功")
            print(f"  特征矩阵形状: {features.shape}")
            print(f"  特征范围: [{features.min():.3f}, {features.max():.3f}]")
        else:
            print("❌ 技术特征计算失败")
            
    except Exception as e:
        print(f"❌ 技术分析器测试失败: {e}")
        traceback.print_exc()

def test_signal_generation(strategy):
    """测试信号生成"""
    print("\n" + "=" * 60)
    print("6. 信号生成测试")
    print("=" * 60)
    
    if strategy is None:
        print("❌ 策略未创建，跳过信号生成测试")
        return
    
    try:
        # 模拟Bar数据
        import numpy as np
        class MockBar:
            def __init__(self):
                self.close_price = 3500.0 + np.random.randn() * 10
                self.open_price = self.close_price + np.random.randn() * 5
                self.high_price = max(self.close_price, self.open_price) + abs(np.random.randn() * 3)
                self.low_price = min(self.close_price, self.open_price) - abs(np.random.randn() * 3)
                self.volume = 1000000 + np.random.randint(-100000, 100000)
        
        # 生成多个信号
        signals = []
        for i in range(5):
            bar = MockBar()
            signal_result = strategy._generate_combined_signal(bar)
            
            if signal_result:
                signals.append(signal_result)
                print(f"  信号 {i+1}: 动作={signal_result['action']}, "
                      f"置信度={signal_result['confidence']:.3f}, "
                      f"来源={signal_result['source']}")
            else:
                print(f"  信号 {i+1}: 无信号生成")
        
        if signals:
            print(f"✅ 信号生成测试成功，生成了 {len(signals)} 个有效信号")
        else:
            print("⚠️  未生成有效信号，可能需要更多数据")
            
    except Exception as e:
        print(f"❌ 信号生成测试失败: {e}")
        traceback.print_exc()

def test_performance_stats(strategy):
    """测试性能统计"""
    print("\n" + "=" * 60)
    print("7. 性能统计测试")
    print("=" * 60)
    
    if strategy is None:
        print("❌ 策略未创建，跳过性能统计测试")
        return
    
    try:
        stats = strategy.performance_stats
        print("✅ 性能统计获取成功")
        print(f"  总信号数: {stats['total_signals']}")
        print(f"  深度学习信号: {stats['dl_signals']}")
        print(f"  Signal System信号: {stats['sys_signals']}")
        print(f"  成功交易: {stats['successful_trades']}")
        print(f"  总盈亏: {stats['total_pnl']:.2f}")
        
    except Exception as e:
        print(f"❌ 性能统计测试失败: {e}")
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 Signal System深度学习策略综合测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入numpy用于测试
    import numpy as np
    np.random.seed(42)  # 设置随机种子以获得可重复的结果
    
    # 执行测试
    results = test_imports()
    strategy = test_strategy_creation()
    test_deep_learning_model(strategy)
    test_signal_system(strategy)
    test_technical_analyzer(strategy)
    test_signal_generation(strategy)
    test_performance_stats(strategy)
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    success_count = sum(1 for status in results.values() if status.startswith('✅'))
    total_count = len(results)
    success_rate = success_count / total_count * 100
    
    print(f"模块导入成功率: {success_rate:.1f}% ({success_count}/{total_count})")
    
    if strategy is not None:
        print("✅ 策略创建和基本功能测试通过")
        print("🎉 策略已准备就绪，可以在VNPY中使用！")
    else:
        print("❌ 策略创建失败，请检查环境配置")
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
