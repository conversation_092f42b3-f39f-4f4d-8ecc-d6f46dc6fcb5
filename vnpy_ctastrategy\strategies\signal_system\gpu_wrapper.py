# -*- coding: utf-8 -*-
"""
V100 GPU信号包装器修复版本
解决Unicode编码问题，兼容VNPY环境
直接使用PyTorch进行GPU计算，不依赖编译的DLL
"""

import os
import sys
import time
import traceback
from typing import Dict, List, Optional, Any, Union
import numpy as np

# PyTorch导入，如果失败则回退到NumPy
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
    print("[GPU_WRAPPER_FIXED] SUCCESS: PyTorch可用")
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print(f"[GPU_WRAPPER_FIXED] SUCCESS: CUDA可用，设备数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"[GPU_WRAPPER_FIXED]   GPU {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("[GPU_WRAPPER_FIXED] WARNING: CUDA不可用，将使用CPU模式")
except ImportError as e:
    print(f"[GPU_WRAPPER_FIXED] WARNING: PyTorch不可用，回退到NumPy模式: {e}")
    TORCH_AVAILABLE = False

class GPUConfig:
    """GPU配置类"""
    def __init__(self, use_gpu=True, device_id=0, use_mixed_precision=True, 
                 use_tensor_cores=True, batch_size=32, max_memory_usage=0.8,
                 optimize_for_v100=True):
        self.use_gpu = use_gpu and TORCH_AVAILABLE
        self.device_id = device_id
        self.use_mixed_precision = use_mixed_precision
        self.use_tensor_cores = use_tensor_cores
        self.batch_size = batch_size
        self.max_memory_usage = max_memory_usage
        self.optimize_for_v100 = optimize_for_v100

class SimpleGRUModel:
    """简化的GRU模型 - 兼容CPU/GPU版本"""
    def __init__(self, input_size=30, hidden_size=64, output_size=3):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        self.using_torch = TORCH_AVAILABLE
        
        if TORCH_AVAILABLE and nn:
            # PyTorch版本
            self.model = nn.Sequential(
                nn.Linear(input_size, hidden_size),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(hidden_size, hidden_size),
                nn.ReLU(),
                nn.Linear(hidden_size, output_size),
                nn.Softmax(dim=-1)
            )
        else:
            # 纯NumPy fallback版本
            print("[INFO] 使用NumPy fallback模型")
            self.weights = self._init_numpy_weights()
    
    def _init_numpy_weights(self):
        """初始化NumPy权重"""
        np.random.seed(42)
        return {
            'w1': np.random.randn(self.input_size, self.hidden_size) * 0.01,
            'b1': np.zeros(self.hidden_size),
            'w2': np.random.randn(self.hidden_size, self.hidden_size) * 0.01,
            'b2': np.zeros(self.hidden_size),
            'w3': np.random.randn(self.hidden_size, self.output_size) * 0.01,
            'b3': np.zeros(self.output_size)
        }
    
    def _relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def _softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def forward(self, x):
        """前向传播"""
        if self.using_torch and hasattr(self, 'model'):
            # PyTorch版本
            if isinstance(x, np.ndarray):
                x = torch.FloatTensor(x)
            return self.model(x)
        else:
            # NumPy版本
            if len(x.shape) > 1:
                x = x.flatten()
            
            # 第一层
            h1 = self._relu(np.dot(x, self.weights['w1']) + self.weights['b1'])
            
            # 第二层
            h2 = self._relu(np.dot(h1, self.weights['w2']) + self.weights['b2'])
            
            # 输出层
            output = np.dot(h2, self.weights['w3']) + self.weights['b3']
            
            return self._softmax(output)
    
    def to(self, device):
        """兼容PyTorch的to方法"""
        if self.using_torch and hasattr(self, 'model'):
            self.model = self.model.to(device)
        return self
    
    def eval(self):
        """兼容PyTorch的eval方法"""
        if self.using_torch and hasattr(self, 'model'):
            self.model.eval()
        return self
    
    def half(self):
        """兼容PyTorch的half方法"""
        if self.using_torch and hasattr(self, 'model'):
            self.model = self.model.half()
        return self

class GPUSignalSystem:
    """V100 GPU优化信号系统 - 纯PyTorch实现"""
    
    def __init__(self, config: Optional[GPUConfig] = None):
        self.config = config or GPUConfig()
        self.device = None
        self.model = None
        self.is_initialized = False
        self.performance_stats = {
            'total_predictions': 0,
            'gpu_time': 0.0,
            'cpu_time': 0.0,
            'memory_usage': 0.0,
            'gpu_available': False,
            'model_type': 'Unknown'
        }
        
        # 初始化GPU
        self._initialize_gpu()
    
    def _initialize_gpu(self):
        """初始化GPU设备"""
        try:
            if not TORCH_AVAILABLE:
                print("[GPU_SIGNAL] PyTorch不可用，使用CPU模式")
                self.device = 'cpu'
                self.performance_stats['model_type'] = 'CPU_Fallback'
                self.performance_stats['gpu_available'] = False
                self._initialize_model()
                self.is_initialized = True
                return
            
            if torch.cuda.is_available():
                self.device = torch.device('cuda:0')
                device_name = torch.cuda.get_device_name(0)
                print(f"[GPU_SIGNAL] 使用GPU: {device_name}")
                self.performance_stats['gpu_available'] = True
                
                # 检查是否为V100
                if 'V100' in device_name:
                    self.performance_stats['model_type'] = 'V100_Optimized'
                    print("[GPU_SIGNAL] 检测到V100，启用优化模式")
                else:
                    self.performance_stats['model_type'] = 'GPU_Generic'
                
                # 设置GPU内存管理
                torch.cuda.empty_cache()
                if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                    torch.cuda.set_per_process_memory_fraction(0.8)
            else:
                self.device = torch.device('cpu')
                print("[GPU_SIGNAL] CUDA不可用，使用CPU模式")
                self.performance_stats['gpu_available'] = False
                self.performance_stats['model_type'] = 'CPU_Mode'
            
            self._initialize_model()
            self.is_initialized = True
            
        except Exception as e:
            print(f"[GPU_SIGNAL] GPU初始化失败: {e}")
            self.device = torch.device('cpu')
            self.performance_stats['gpu_available'] = False
            self.performance_stats['model_type'] = 'CPU_Error_Fallback'
            self._initialize_model()
            self.is_initialized = True
    
    def _initialize_model(self):
        """初始化神经网络模型"""
        try:
            if not TORCH_AVAILABLE:
                print("[GPU_SIGNAL] 使用简化CPU模型")
                self.model = None
                return
            
            # 创建简单的LSTM模型用于信号预测
            class SignalLSTM(nn.Module):
                def __init__(self, input_size=5, hidden_size=64, num_layers=2, output_size=3):
                    super(SignalLSTM, self).__init__()
                    self.hidden_size = hidden_size
                    self.num_layers = num_layers
                    
                    self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=0.2)
                    self.fc = nn.Linear(hidden_size, output_size)
                    self.softmax = nn.Softmax(dim=1)
                
                def forward(self, x):
                    h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
                    c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
                    
                    out, _ = self.lstm(x, (h0, c0))
                    out = self.fc(out[:, -1, :])
                    return self.softmax(out)
            
            self.model = SignalLSTM().to(self.device)
            print(f"[GPU_SIGNAL] 模型已初始化到设备: {self.device}")
            
        except Exception as e:
            print(f"[GPU_SIGNAL] 模型初始化失败: {e}")
            self.model = None
    
    def generate_signal(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易信号"""
        start_time = time.time()
        
        try:
            # 提取市场数据特征
            features = self._extract_features(market_data)
            
            if self.model is not None and TORCH_AVAILABLE:
                # GPU/CPU模式预测
                signal = self._predict_with_model(features)
            else:
                # 简化规则模式
                signal = self._predict_with_rules(features)
            
            # 更新性能统计
            elapsed_time = time.time() - start_time
            self.performance_stats['total_predictions'] += 1
            
            if self.performance_stats['gpu_available']:
                self.performance_stats['gpu_time'] += elapsed_time
            else:
                self.performance_stats['cpu_time'] += elapsed_time
            
            return {
                'signal': signal,
                'confidence': 0.8,
                'timestamp': time.time(),
                'processing_time': elapsed_time,
                'device': str(self.device)
            }
            
        except Exception as e:
            print(f"[GPU_SIGNAL] 信号生成失败: {e}")
            return {
                'signal': 0,
                'confidence': 0.0,
                'timestamp': time.time(),
                'error': str(e)
            }
    
    def _extract_features(self, market_data: Dict[str, Any]) -> np.ndarray:
        """提取市场数据特征"""
        try:
            # 基本特征提取
            price = market_data.get('close', 0.0)
            volume = market_data.get('volume', 0.0)
            high = market_data.get('high', price)
            low = market_data.get('low', price)
            open_price = market_data.get('open', price)
            
            # 计算技术指标
            price_change = (price - open_price) / open_price if open_price > 0 else 0
            volatility = (high - low) / price if price > 0 else 0
            volume_ratio = volume / 1000000  # 标准化
            
            features = np.array([price_change, volatility, volume_ratio, 
                               np.sin(time.time() % 3600), np.cos(time.time() % 3600)])
            
            return features.reshape(1, 1, -1)  # (batch, seq, features)
            
        except Exception as e:
            print(f"[GPU_SIGNAL] 特征提取失败: {e}")
            return np.zeros((1, 1, 5))
    
    def _predict_with_model(self, features: np.ndarray) -> int:
        """使用神经网络模型预测"""
        try:
            with torch.no_grad():
                x = torch.FloatTensor(features).to(self.device)
                output = self.model(x)
                prediction = torch.argmax(output, dim=1).item()
                return prediction - 1  # 转换为 -1, 0, 1
                
        except Exception as e:
            print(f"[GPU_SIGNAL] 模型预测失败: {e}")
            return 0
    
    def _predict_with_rules(self, features: np.ndarray) -> int:
        """使用简单规则预测"""
        try:
            price_change = features[0, 0, 0]
            volatility = features[0, 0, 1]
            
            if price_change > 0.01 and volatility < 0.02:
                return 1  # 买入
            elif price_change < -0.01 and volatility < 0.02:
                return -1  # 卖出
            else:
                return 0  # 持有
                
        except Exception as e:
            print(f"[GPU_SIGNAL] 规则预测失败: {e}")
            return 0
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        
        # 计算吞吐量
        total_time = stats['gpu_time'] + stats['cpu_time']
        if total_time > 0:
            stats['throughput'] = stats['total_predictions'] / total_time
        else:
            stats['throughput'] = 0.0
        
        # 添加内存使用情况
        if TORCH_AVAILABLE and torch.cuda.is_available():
            try:
                stats['gpu_memory_allocated'] = torch.cuda.memory_allocated() / 1024**2  # MB
                stats['gpu_memory_cached'] = torch.cuda.memory_reserved() / 1024**2  # MB
            except:
                stats['gpu_memory_allocated'] = 0.0
                stats['gpu_memory_cached'] = 0.0
        
        return stats
    
    def cleanup(self):
        """清理资源"""
        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                torch.cuda.empty_cache()
            print("[GPU_SIGNAL] 资源清理完成")
        except Exception as e:
            print(f"[GPU_SIGNAL] 资源清理失败: {e}")
 
class GPUSignalSystemWrapper:
    """V100优化的GPU信号系统包装器 - 兼容无torch环境"""
    
    def __init__(self, config: Optional[GPUConfig] = None):
        self.config = config or GPUConfig()
        self.device = None
        self.model = None
        self.is_ready = False
        self.using_gpu = False
        self.using_torch = TORCH_AVAILABLE
        
        # 性能统计
        self.total_predictions = 0
        self.total_inference_time = 0.0
        self.avg_memory_usage = 0.0
        
    def initialize(self, input_size=30, hidden_size=64, output_size=3):
        """初始化GPU信号系统"""
        try:
            # 检查GPU可用性
            if TORCH_AVAILABLE and torch.cuda.is_available() and self.config.use_gpu:
                self.device = torch.device(f"cuda:{self.config.device_id}")
                self.using_gpu = True
                print(f"[SUCCESS] 使用GPU: {torch.cuda.get_device_name(self.device)}")
                
                # V100专用优化
                if self.config.optimize_for_v100:
                    torch.backends.cudnn.benchmark = True
                    torch.backends.cuda.matmul.allow_tf32 = True
                    if self.config.use_tensor_cores:
                        torch.backends.cudnn.allow_tf32 = True
                
            elif TORCH_AVAILABLE:
                self.device = torch.device("cpu")
                self.using_gpu = False
                print("[INFO] 使用CPU模式 (PyTorch)")
                
            else:
                self.device = "cpu"
                self.using_gpu = False
                print("[INFO] 使用CPU模式 (NumPy fallback)")
            
            # 创建模型
            self.model = SimpleGRUModel(input_size, hidden_size, output_size)
            self.model.to(self.device)
            
            if self.using_gpu and self.config.use_mixed_precision and TORCH_AVAILABLE:
                self.model = self.model.half()  # 转换为FP16
                print("[SUCCESS] 启用FP16混合精度")
            
            # 设置为评估模式
            self.model.eval()
            
            self.is_ready = True
            print("[SUCCESS] 信号系统初始化成功")
            return True
            
        except Exception as e:
            print(f"[ERROR] 初始化失败: {e}")
            # 回退到纯NumPy模式
            try:
                self.device = "cpu"
                self.using_gpu = False
                self.using_torch = False
                self.model = SimpleGRUModel(input_size, hidden_size, output_size)
                self.model.eval()
                self.is_ready = True
                print("[SUCCESS] 回退到NumPy CPU模式成功")
                return True
            except Exception as e2:
                print(f"[ERROR] NumPy回退也失败: {e2}")
                return False
    
    def get_signal(self, features: List[float]) -> Dict[str, Any]:
        """获取交易信号"""
        if not self.is_ready:
            return {
                'direction': 0,
                'confidence': 0.5,
                'using_gpu': False,
                'model_type': 'Uninitialized',
                'inference_time_ms': 0.0
            }
        
        start_time = time.time()
        
        try:
            # 预处理特征
            if len(features) < 30:
                features = features + [0.0] * (30 - len(features))
            elif len(features) > 30:
                features = features[:30]
            
            # 转换输入
            if self.using_torch and TORCH_AVAILABLE:
                input_tensor = torch.FloatTensor([features]).unsqueeze(0)  # (1, 1, 30)
                
                if self.using_gpu:
                    input_tensor = input_tensor.to(self.device)
                    if self.config.use_mixed_precision:
                        input_tensor = input_tensor.half()
                
                # 推理
                with torch.no_grad():
                    if self.using_gpu:
                        torch.cuda.synchronize()  # 确保GPU计算完成
                    
                    predictions = self.model.forward(input_tensor.squeeze(0))
                    
                    if self.using_gpu:
                        torch.cuda.synchronize()
                        predictions = predictions.cpu()
                    
                    # 转换为numpy
                    if isinstance(predictions, torch.Tensor):
                        probs = predictions.numpy().flatten()
                    else:
                        probs = predictions.flatten()
            else:
                # NumPy版本
                input_array = np.array(features, dtype=np.float32)
                probs = self.model.forward(input_array)
                if len(probs.shape) > 1:
                    probs = probs.flatten()
            
            # 解析结果
            direction = np.argmax(probs) - 1  # 0,1,2 -> -1,0,1
            confidence = float(np.max(probs))
                
        except Exception as e:
            print(f"[WARNING] 推理失败: {e}")
            direction = 0
            confidence = 0.5
        
        end_time = time.time()
        inference_time = (end_time - start_time) * 1000  # 毫秒
        
        # 更新统计
        self.total_predictions += 1
        self.total_inference_time += inference_time
        
        # 确定模型类型
        if self.using_torch and self.using_gpu:
            model_type = "V100_GPU"
            if self.config.use_mixed_precision:
                model_type += "_FP16"
        elif self.using_torch:
            model_type = "PyTorch_CPU"
        else:
            model_type = "NumPy_CPU_Fallback"
        
        return {
            'direction': direction,
            'confidence': confidence,
            'using_gpu': self.using_gpu,
            'model_type': model_type,
            'inference_time_ms': inference_time
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_inference_time = (self.total_inference_time / max(1, self.total_predictions))
        
        memory_usage = 0.0
        device_name = "CPU"
        
        if self.using_gpu and TORCH_AVAILABLE and torch.cuda.is_available():
            try:
                memory_usage = torch.cuda.memory_allocated(self.device) / 1024**2  # MB
                device_name = torch.cuda.get_device_name(self.device)
            except:
                device_name = "GPU (信息获取失败)"
        elif self.using_torch:
            device_name = "CPU (PyTorch)"
        else:
            device_name = "CPU (NumPy)"
        
        return {
            'total_predictions': self.total_predictions,
            'avg_inference_time_ms': avg_inference_time,
            'avg_memory_usage_mb': memory_usage,
            'using_gpu': self.using_gpu,
            'device_name': device_name,
            'torch_available': TORCH_AVAILABLE
        }
    
    def cleanup(self):
        """清理资源"""
        if self.using_gpu and TORCH_AVAILABLE and torch.cuda.is_available():
            try:
                torch.cuda.empty_cache()
            except:
                pass

def create_v100_signal_system(use_mixed_precision=True, **kwargs):
    """创建V100优化的信号系统"""
    config = GPUConfig(use_mixed_precision=use_mixed_precision, **kwargs)
    wrapper = GPUSignalSystemWrapper(config)
    wrapper.initialize()
    return wrapper

class GPUSignalWrapperFixed(GPUSignalSystemWrapper):
    """修复版本的GPU信号包装器 - 主要接口类"""
    
    def __init__(self, config: Optional[GPUConfig] = None):
        super().__init__(config)
        print(f"[INFO] PyTorch可用: {TORCH_AVAILABLE}")
        # 自动初始化
        self.initialize()
        
    def predict(self, features: List[float]) -> Dict[str, Any]:
        """预测接口 - 兼容旧版本"""
        return self.get_signal(features)
        
    def predict_batch(self, features_list: List[List[float]]) -> List[Dict[str, Any]]:
        """批量预测接口"""
        results = []
        for features in features_list:
            results.append(self.get_signal(features))
        return results

# 测试函数
if __name__ == "__main__":
    print("[INFO] 测试GPU信号包装器...")
    wrapper = GPUSignalWrapperFixed()
    
    if wrapper.initialize():
        # 测试信号生成
        test_features = [0.1 * i for i in range(30)]
        result = wrapper.get_signal(test_features)
        
        print(f"[SUCCESS] 测试结果: {result}")
        print(f"[INFO] 性能统计: {wrapper.get_performance_stats()}")
    else:
        print("[ERROR] 初始化失败")