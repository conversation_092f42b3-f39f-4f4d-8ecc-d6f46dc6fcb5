# -*- coding: utf-8 -*-
"""
Signal System Module for VNPY Deep Learning Strategies
信号系统模块 - 用于VNPY深度学习策略

包含以下组件:
- GPU信号包装器 (gpu_wrapper.py)
- 深度学习模型接口
- V100 GPU优化支持
- VNPY策略集成接口
"""

__version__ = "1.0.0"
__author__ = "VNPY + Signal System Team"

# 导出主要类和函数
try:
    from .gpu_wrapper import GPUSignalWrapperFixed
    from .signal_system_wrapper import SignalSystemWrapper
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    print("[WARNING] GPU信号系统不可用，请检查依赖")

__all__ = [
    "GPUSignalWrapperFixed",
    "SignalSystemWrapper",
    "GPU_AVAILABLE"
]

print(f"[INFO] Signal System Module v{__version__} 已加载")
if GPU_AVAILABLE:
    print("[INFO] GPU信号系统可用")
else:
    print("[WARNING] GPU信号系统不可用")

import warnings

# 导入GPU包装器
from .gpu_signal_wrapper_fixed import create_v100_signal_system

# 暂时跳过signal_system模块导入，避免DLL依赖问题
signal_system = None
print("[INFO] 跳过signal_system模块导入，使用GPU包装器模式")

# 简化导出，只导出GPU包装器
__all__ = ['create_v100_signal_system']