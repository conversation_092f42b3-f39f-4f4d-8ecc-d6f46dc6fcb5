# SimpleSignalStrategy 使用说明


🎯 在VNPY中使用SimpleSignalStrategy的步骤:

1. 🔄 重启VeighNa Studio
   - 完全关闭VeighNa Studio
   - 重新启动应用程序

2. 📊 进入CTA策略模块
   - 点击"CTA策略"选项卡

3. 🔍 查找策略
   - 在策略列表中查找 "SimpleSignalStrategy"
   - 这是最简化、最兼容的版本

4. ⚙️ 配置策略参数
   策略名称: SimpleSignalStrategy
   交易品种: rb2501.SHFE (螺纹钢主力)
   参数配置:
   - confidence_threshold: 0.6 (置信度阈值)
   - position_size: 1 (仓位大小)
   - stop_loss_pct: 0.02 (止损2%)
   - take_profit_pct: 0.04 (止盈4%)
   - ma_period: 20 (移动平均周期)

5. 🚀 启动策略
   - 点击"初始化"按钮
   - 点击"启动"按钮

📋 策略特点:
- ✅ 完全兼容VNPY，无外部依赖
- ✅ 基于移动平均线的趋势跟踪
- ✅ 成交量确认机制
- ✅ 自动止损止盈
- ✅ 防止过度频繁交易

⚠️ 注意事项:
- 首次使用建议在模拟环境测试
- 确保有足够资金和风险承受能力
- 定期检查策略表现
- 根据市场情况调整参数

🔧 如果仍然看不到策略:
1. 检查VNPY版本是否为4.1.0+
2. 确认CTA策略模块已正确加载
3. 查看VNPY启动日志是否有错误
4. 尝试手动刷新策略列表
