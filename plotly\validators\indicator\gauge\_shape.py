import _plotly_utils.basevalidators


class ShapeValidator(_plotly_utils.basevalidators.EnumeratedValidator):
    def __init__(self, plotly_name="shape", parent_name="indicator.gauge", **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>da<PERSON>, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "plot"),
            values=kwargs.pop("values", ["angular", "bullet"]),
            **kwargs,
        )
