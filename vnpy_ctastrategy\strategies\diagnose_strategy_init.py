#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
策略初始化问题诊断脚本
检查IndependentSignalStrategy的初始化问题
"""

import sys
import traceback
from pathlib import Path

def test_basic_imports():
    """测试基础导入"""
    print("=" * 60)
    print("1. 基础导入测试")
    print("=" * 60)
    
    try:
        from vnpy_ctastrategy import CtaTemplate, BarGenerator, ArrayManager
        print("✅ VNPY CTA基础组件导入成功")
    except ImportError as e:
        print(f"❌ VNPY CTA组件导入失败: {e}")
        return False
    
    try:
        from vnpy.trader.constant import Direction, Offset, Status
        print("✅ VNPY交易常量导入成功")
    except ImportError as e:
        print(f"❌ VNPY交易常量导入失败: {e}")
        return False
    
    return True

def test_strategy_import():
    """测试策略导入"""
    print("\n" + "=" * 60)
    print("2. 策略导入测试")
    print("=" * 60)
    
    try:
        from vnpy_ctastrategy.strategies.independent_signal_strategy import IndependentSignalStrategy
        print("✅ IndependentSignalStrategy导入成功")
        
        # 检查策略属性
        print(f"   作者: {IndependentSignalStrategy.author}")
        print(f"   参数数量: {len(IndependentSignalStrategy.parameters)}")
        print(f"   变量数量: {len(IndependentSignalStrategy.variables)}")
        
        return IndependentSignalStrategy
        
    except ImportError as e:
        print(f"❌ 策略导入失败: {e}")
        traceback.print_exc()
        return None
    except Exception as e:
        print(f"❌ 策略导入异常: {e}")
        traceback.print_exc()
        return None

def test_strategy_instantiation(strategy_class):
    """测试策略实例化"""
    print("\n" + "=" * 60)
    print("3. 策略实例化测试")
    print("=" * 60)
    
    if strategy_class is None:
        print("❌ 策略类不可用，跳过实例化测试")
        return None
    
    try:
        # 创建模拟CTA引擎
        class MockCtaEngine:
            def __init__(self):
                self.main_engine = None
                
            def write_log(self, msg):
                print(f"    [策略日志] {msg}")
                
            def send_order(self, strategy, direction, offset, price, volume, stop=False, lock=False, net=False):
                print(f"    [模拟下单] {direction} {offset} {volume}@{price}")
                return "test_order_id"
                
            def cancel_order(self, strategy, vt_orderid):
                print(f"    [模拟撤单] {vt_orderid}")
                
            def get_contract(self, vt_symbol):
                # 返回模拟合约信息
                class MockContract:
                    def __init__(self):
                        self.symbol = "rb2501"
                        self.exchange = "SHFE"
                        self.name = "螺纹钢2501"
                        self.product = "rb"
                        self.size = 10
                        self.pricetick = 1
                        self.min_volume = 1
                        
                return MockContract()
        
        # 创建策略实例
        mock_engine = MockCtaEngine()
        strategy = strategy_class(
            cta_engine=mock_engine,
            strategy_name="test_independent_strategy",
            vt_symbol="rb2501.SHFE",
            setting={
                "confidence_threshold": 0.6,
                "position_size": 1,
                "ma_period": 20
            }
        )
        
        print("✅ 策略实例化成功")
        print(f"   策略名称: {strategy.strategy_name}")
        print(f"   交易品种: {strategy.vt_symbol}")
        print(f"   置信度阈值: {strategy.confidence_threshold}")
        
        return strategy
        
    except Exception as e:
        print(f"❌ 策略实例化失败: {e}")
        traceback.print_exc()
        return None

def test_strategy_initialization(strategy):
    """测试策略初始化"""
    print("\n" + "=" * 60)
    print("4. 策略初始化测试")
    print("=" * 60)
    
    if strategy is None:
        print("❌ 策略实例不可用，跳过初始化测试")
        return False
    
    try:
        # 测试on_init方法
        print("测试on_init方法...")
        strategy.on_init()
        print("✅ on_init方法执行成功")
        
        # 检查ArrayManager初始化
        if hasattr(strategy, 'am'):
            print(f"✅ ArrayManager已创建，大小: {strategy.am.size}")
        else:
            print("❌ ArrayManager未创建")
        
        # 检查BarGenerator初始化
        if hasattr(strategy, 'bg'):
            print("✅ BarGenerator已创建")
        else:
            print("❌ BarGenerator未创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略初始化失败: {e}")
        traceback.print_exc()
        return False

def test_strategy_start(strategy):
    """测试策略启动"""
    print("\n" + "=" * 60)
    print("5. 策略启动测试")
    print("=" * 60)
    
    if strategy is None:
        print("❌ 策略实例不可用，跳过启动测试")
        return False
    
    try:
        # 测试on_start方法
        print("测试on_start方法...")
        strategy.on_start()
        print("✅ on_start方法执行成功")
        
        # 检查trading状态
        if hasattr(strategy, 'trading') and strategy.trading:
            print("✅ 策略交易状态已启用")
        else:
            print("⚠️  策略交易状态未启用")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略启动失败: {e}")
        traceback.print_exc()
        return False

def test_bar_processing(strategy):
    """测试K线处理"""
    print("\n" + "=" * 60)
    print("6. K线处理测试")
    print("=" * 60)
    
    if strategy is None:
        print("❌ 策略实例不可用，跳过K线测试")
        return False
    
    try:
        # 创建模拟K线数据
        from datetime import datetime
        
        class MockBar:
            def __init__(self, close_price, volume=1000000):
                self.symbol = "rb2501"
                self.exchange = "SHFE"
                self.datetime = datetime.now()
                self.interval = "1m"
                self.volume = volume
                self.turnover = close_price * volume
                self.open_interest = 100000
                self.open_price = close_price - 5
                self.high_price = close_price + 10
                self.low_price = close_price - 10
                self.close_price = close_price
        
        # 测试多根K线
        print("测试K线数据处理...")
        base_price = 3500
        
        for i in range(25):  # 添加25根K线确保ArrayManager初始化
            price = base_price + i * 2  # 模拟价格上涨
            bar = MockBar(price, 1000000 + i * 10000)
            
            try:
                strategy.on_1min_bar(bar)
                if i == 0:
                    print("✅ 第一根K线处理成功")
                elif i == 20:
                    print("✅ ArrayManager应该已初始化")
                elif i == 24:
                    print("✅ 最后一根K线处理成功")
            except Exception as e:
                print(f"❌ 第{i+1}根K线处理失败: {e}")
                traceback.print_exc()
                return False
        
        # 检查策略状态
        print(f"✅ K线处理完成")
        print(f"   当前价格: {strategy.last_price}")
        print(f"   MA值: {strategy.ma_value}")
        print(f"   信号数量: {strategy.signal_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ K线处理测试失败: {e}")
        traceback.print_exc()
        return False

def create_fixed_strategy():
    """创建修复版策略"""
    print("\n" + "=" * 60)
    print("7. 创建修复版策略")
    print("=" * 60)
    
    fixed_strategy_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复版独立Signal System策略
解决初始化问题
"""

from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
from vnpy.trader.constant import Direction, Offset, Status

class FixedIndependentStrategy(CtaTemplate):
    """修复版独立Signal System策略"""
    
    author = "VNPY + Signal System (Fixed)"
    
    # 策略参数
    confidence_threshold = 0.6
    position_size = 1
    stop_loss_pct = 0.02
    take_profit_pct = 0.04
    ma_period = 20
    
    # 策略变量
    current_position = 0
    entry_price = 0.0
    signal_confidence = 0.0
    signal_count = 0
    ma_value = 0.0
    
    parameters = [
        "confidence_threshold", 
        "position_size", 
        "stop_loss_pct", 
        "take_profit_pct",
        "ma_period"
    ]
    variables = [
        "current_position", 
        "entry_price", 
        "signal_confidence", 
        "signal_count",
        "ma_value"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 初始化VNPY组件
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=50)  # 减小ArrayManager大小
        
        # 简化数据缓存
        self.price_history = []
        self.last_signal_time = 0
        
        self.write_log("修复版策略初始化完成")
        
    def on_init(self):
        """策略初始化"""
        self.write_log("修复版策略开始初始化")
        try:
            self.load_bar(30)
            self.write_log("历史数据加载完成")
        except Exception as e:
            self.write_log(f"历史数据加载失败: {e}")
        
    def on_start(self):
        """策略启动"""
        self.write_log("修复版策略启动")
        self.trading = True
        
    def on_stop(self):
        """策略停止"""
        self.write_log("修复版策略停止")
        self.trading = False
        
    def on_tick(self, tick: TickData):
        """处理tick数据"""
        self.bg.update_tick(tick)
        
    def on_bar(self, bar: BarData):
        """处理bar数据"""
        self.bg.update_bar(bar)
        
    def on_1min_bar(self, bar: BarData):
        """处理1分钟K线"""
        try:
            self.am.update_bar(bar)
            if not self.am.inited:
                return
                
            # 更新价格历史
            self.price_history.append(bar.close_price)
            if len(self.price_history) > 50:
                self.price_history.pop(0)
            
            # 计算移动平均
            if len(self.am.close_array) >= self.ma_period:
                self.ma_value = self.am.sma(self.ma_period)
            
            # 简化信号生成
            signal_result = self._generate_simple_signal(bar)
            
            # 执行交易逻辑
            if signal_result and signal_result['confidence'] > self.confidence_threshold:
                self._execute_trading_logic(signal_result, bar)
                
            # 检查止损止盈
            self._check_stop_conditions(bar)
            
            self.put_event()
            
        except Exception as e:
            self.write_log(f"K线处理异常: {e}")
    
    def _generate_simple_signal(self, bar):
        """生成简化交易信号"""
        try:
            if len(self.price_history) < 5 or self.ma_value == 0:
                return None
            
            current_price = bar.close_price
            price_ma_ratio = (current_price - self.ma_value) / self.ma_value
            
            # 简单动量计算
            if len(self.price_history) >= 3:
                price_change = (self.price_history[-1] - self.price_history[-3]) / self.price_history[-3]
            else:
                price_change = 0
            
            signal = 0
            confidence = 0.5
            
            # 简化信号逻辑
            if price_ma_ratio > 0.01 and price_change > 0.005:
                signal = 1
                confidence = 0.7
            elif price_ma_ratio < -0.01 and price_change < -0.005:
                signal = -1
                confidence = 0.7
            
            return {
                'action': signal,
                'confidence': confidence,
                'price_ma_ratio': price_ma_ratio,
                'price_change': price_change
            }
            
        except Exception as e:
            self.write_log(f"信号生成异常: {e}")
            return None
    
    def _execute_trading_logic(self, signal_result, bar):
        """执行交易逻辑"""
        try:
            action = signal_result['action']
            confidence = signal_result['confidence']
            
            self.signal_confidence = confidence
            self.signal_count += 1
            
            current_price = bar.close_price
            import time
            current_time = time.time()
            
            # 防止频繁交易
            if current_time - self.last_signal_time < 60:
                return
            
            # 买入信号
            if action == 1 and self.pos <= 0:
                if self.pos < 0:
                    self.cover(current_price, abs(self.pos))
                self.buy(current_price, self.position_size)
                self.entry_price = current_price
                self.last_signal_time = current_time
                self.write_log(f"买入: 价格={current_price}, 置信度={confidence:.3f}")
                
            # 卖出信号
            elif action == -1 and self.pos >= 0:
                if self.pos > 0:
                    self.sell(current_price, self.pos)
                self.short(current_price, self.position_size)
                self.entry_price = current_price
                self.last_signal_time = current_time
                self.write_log(f"卖出: 价格={current_price}, 置信度={confidence:.3f}")
                
        except Exception as e:
            self.write_log(f"交易执行异常: {e}")
    
    def _check_stop_conditions(self, bar):
        """检查止损止盈"""
        try:
            if self.pos == 0 or self.entry_price == 0:
                return
                
            current_price = bar.close_price
            
            if self.pos > 0:
                pnl_pct = (current_price - self.entry_price) / self.entry_price
                if pnl_pct <= -self.stop_loss_pct:
                    self.sell(current_price, self.pos)
                    self.write_log(f"多头止损: {pnl_pct:.2%}")
                    self.entry_price = 0
                elif pnl_pct >= self.take_profit_pct:
                    self.sell(current_price, self.pos)
                    self.write_log(f"多头止盈: {pnl_pct:.2%}")
                    self.entry_price = 0
                    
            elif self.pos < 0:
                pnl_pct = (self.entry_price - current_price) / self.entry_price
                if pnl_pct <= -self.stop_loss_pct:
                    self.cover(current_price, abs(self.pos))
                    self.write_log(f"空头止损: {pnl_pct:.2%}")
                    self.entry_price = 0
                elif pnl_pct >= self.take_profit_pct:
                    self.cover(current_price, abs(self.pos))
                    self.write_log(f"空头止盈: {pnl_pct:.2%}")
                    self.entry_price = 0
                    
        except Exception as e:
            self.write_log(f"止损止盈检查异常: {e}")
    
    def on_trade(self, trade: TradeData):
        """处理成交回报"""
        self.current_position = self.pos
        self.put_event()
    
    def on_order(self, order: OrderData):
        """处理委托回报"""
        pass
    
    def on_stop_order(self, stop_order: StopOrder):
        """处理停止单回报"""
        pass
'''
    
    # 保存修复版策略
    current_dir = Path(__file__).parent
    fixed_file = current_dir / "fixed_independent_strategy.py"
    
    with open(fixed_file, 'w', encoding='utf-8') as f:
        f.write(fixed_strategy_content)
    
    print(f"✅ 修复版策略已创建: {fixed_file}")
    print("📋 修复内容:")
    print("   - 简化了信号生成逻辑")
    print("   - 减小了ArrayManager大小")
    print("   - 增加了异常处理")
    print("   - 优化了初始化流程")
    
    return True

def main():
    """主函数"""
    print("🔍 IndependentSignalStrategy初始化问题诊断")
    print(f"诊断时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行诊断
    test1 = test_basic_imports()
    if not test1:
        print("\n❌ 基础导入失败，请先修复VNPY环境")
        return
    
    strategy_class = test_strategy_import()
    strategy = test_strategy_instantiation(strategy_class)
    test4 = test_strategy_initialization(strategy)
    test5 = test_strategy_start(strategy)
    test6 = test_bar_processing(strategy)
    
    # 如果测试失败，创建修复版
    if not all([test4, test5, test6]):
        print("\n🔧 检测到初始化问题，创建修复版策略...")
        create_fixed_strategy()
        
        print("\n" + "=" * 60)
        print("解决方案")
        print("=" * 60)
        print("📋 问题分析:")
        print("   - 原策略可能过于复杂")
        print("   - ArrayManager初始化问题")
        print("   - 信号生成逻辑异常")
        
        print("\n🔧 修复方案:")
        print("1. 使用修复版策略: FixedIndependentStrategy")
        print("2. 在VNPY中查找 'FixedIndependentStrategy'")
        print("3. 配置参数并启动")
        
        print("\n⚙️ 推荐配置:")
        print("   - confidence_threshold: 0.6")
        print("   - position_size: 1")
        print("   - ma_period: 20")
        
    else:
        print("\n🎉 策略诊断通过！")
        print("策略应该能正常初始化和运行")
    
    print(f"\n诊断完成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
