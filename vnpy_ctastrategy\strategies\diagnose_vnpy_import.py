#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNPY策略导入诊断脚本
检查策略在VNPY环境中的导入问题
"""

import os
import sys
import traceback
from pathlib import Path

def test_vnpy_environment():
    """测试VNPY环境"""
    print("=" * 60)
    print("VNPY环境诊断")
    print("=" * 60)
    
    try:
        import vnpy
        print(f"✅ VNPY版本: {vnpy.__version__}")
    except ImportError as e:
        print(f"❌ VNPY导入失败: {e}")
        return False
    
    try:
        import vnpy_ctastrategy
        print(f"✅ VNPY CTA策略模块版本: {vnpy_ctastrategy.__version__}")
    except ImportError as e:
        print(f"❌ VNPY CTA策略模块导入失败: {e}")
        return False
    
    try:
        from vnpy_ctastrategy import CtaTemplate
        print("✅ CtaTemplate基类可用")
    except ImportError as e:
        print(f"❌ CtaTemplate导入失败: {e}")
        return False
    
    return True

def test_strategy_import_in_vnpy():
    """在VNPY环境中测试策略导入"""
    print("\n" + "=" * 60)
    print("策略导入测试 (VNPY环境)")
    print("=" * 60)
    
    # 测试策略模块导入
    try:
        import vnpy_ctastrategy.strategies as strategies_module
        print("✅ 策略模块导入成功")
        
        # 检查__all__
        if hasattr(strategies_module, '__all__'):
            all_strategies = strategies_module.__all__
            print(f"✅ 发现 {len(all_strategies)} 个注册策略")
            
            # 检查我们的策略是否在列表中
            our_strategies = ['SignalSystemStrategy', 'SignalSystemDeepLearningStrategy']
            for strategy_name in our_strategies:
                if strategy_name in all_strategies:
                    print(f"   ✅ {strategy_name} - 已注册")
                else:
                    print(f"   ❌ {strategy_name} - 未注册")
        else:
            print("❌ 策略模块没有__all__属性")
            
    except ImportError as e:
        print(f"❌ 策略模块导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 测试具体策略导入
    strategies_to_test = [
        'SignalSystemStrategy',
        'SignalSystemDeepLearningStrategy'
    ]
    
    for strategy_name in strategies_to_test:
        try:
            strategy_class = getattr(strategies_module, strategy_name, None)
            if strategy_class:
                print(f"✅ {strategy_name} - 类可获取")
                print(f"   作者: {getattr(strategy_class, 'author', 'Unknown')}")
                print(f"   参数: {getattr(strategy_class, 'parameters', [])}")
            else:
                print(f"❌ {strategy_name} - 类不存在")
        except Exception as e:
            print(f"❌ {strategy_name} - 获取失败: {e}")
    
    return True

def test_direct_strategy_import():
    """直接测试策略文件导入"""
    print("\n" + "=" * 60)
    print("直接策略文件导入测试")
    print("=" * 60)
    
    current_dir = Path(__file__).parent
    
    # 测试SignalSystemStrategy
    try:
        from vnpy_ctastrategy.strategies.signal_system_strategy import SignalSystemStrategy
        print("✅ SignalSystemStrategy 直接导入成功")
        
        # 测试实例化
        class MockEngine:
            def write_log(self, msg): 
                print(f"    [策略] {msg}")
        
        strategy = SignalSystemStrategy(
            cta_engine=MockEngine(),
            strategy_name="test_strategy",
            vt_symbol="rb2501.SHFE",
            setting={}
        )
        print("✅ SignalSystemStrategy 实例化成功")
        
    except ImportError as e:
        print(f"❌ SignalSystemStrategy 导入失败: {e}")
        traceback.print_exc()
    except Exception as e:
        print(f"❌ SignalSystemStrategy 实例化失败: {e}")
        traceback.print_exc()

def check_vnpy_strategy_discovery():
    """检查VNPY策略发现机制"""
    print("\n" + "=" * 60)
    print("VNPY策略发现机制检查")
    print("=" * 60)
    
    try:
        # 模拟VNPY的策略发现过程
        from vnpy.app.cta_strategy.engine import CtaEngine
        
        # 获取所有可用策略类
        strategy_classes = CtaEngine.get_all_strategy_class_names()
        print(f"✅ VNPY发现 {len(strategy_classes)} 个策略类:")
        
        our_strategies = ['SignalSystemStrategy', 'SignalSystemDeepLearningStrategy']
        found_count = 0
        
        for strategy_name in strategy_classes:
            if strategy_name in our_strategies:
                print(f"   🎯 {strategy_name} - 已被VNPY发现")
                found_count += 1
            else:
                print(f"   📋 {strategy_name}")
        
        if found_count == 0:
            print("\n❌ 我们的策略未被VNPY发现")
            return False
        else:
            print(f"\n✅ {found_count}/{len(our_strategies)} 个策略被VNPY发现")
            return True
            
    except Exception as e:
        print(f"❌ VNPY策略发现检查失败: {e}")
        traceback.print_exc()
        return False

def create_fixed_init_file():
    """创建修复的__init__.py文件"""
    print("\n" + "=" * 60)
    print("创建修复的__init__.py文件")
    print("=" * 60)
    
    current_dir = Path(__file__).parent
    init_file = current_dir / "__init__.py"
    
    # 创建更简单、更兼容的__init__.py
    fixed_content = '''# -*- coding: utf-8 -*-
"""
VNPY策略模块 - 修复版
确保策略能被VNPY正确识别
"""

# 导入现有策略
from .double_ma_strategy import DoubleMaStrategy
from .atr_rsi_strategy import AtrRsiStrategy
from .boll_channel_strategy import BollChannelStrategy
from .dual_thrust_strategy import DualThrustStrategy
from .king_keltner_strategy import KingKeltnerStrategy
from .multi_signal_strategy import MultiSignalStrategy
from .multi_timeframe_strategy import MultiTimeframeStrategy
from .turtle_signal_strategy import TurtleSignalStrategy

# 导入我们的新策略
try:
    from .signal_system_strategy import SignalSystemStrategy
    SIGNAL_SYSTEM_AVAILABLE = True
    print("[VNPY] SignalSystemStrategy 导入成功")
except Exception as e:
    SIGNAL_SYSTEM_AVAILABLE = False
    print(f"[VNPY] SignalSystemStrategy 导入失败: {e}")

try:
    from .signal_system_deep_learning_strategy import SignalSystemDeepLearningStrategy
    SIGNAL_DL_AVAILABLE = True
    print("[VNPY] SignalSystemDeepLearningStrategy 导入成功")
except Exception as e:
    SIGNAL_DL_AVAILABLE = False
    print(f"[VNPY] SignalSystemDeepLearningStrategy 导入失败: {e}")

# 策略列表 - 确保我们的策略在前面
__all__ = []

# 添加我们的策略
if SIGNAL_SYSTEM_AVAILABLE:
    __all__.append("SignalSystemStrategy")
if SIGNAL_DL_AVAILABLE:
    __all__.append("SignalSystemDeepLearningStrategy")

# 添加现有策略
__all__.extend([
    "DoubleMaStrategy",
    "AtrRsiStrategy", 
    "BollChannelStrategy",
    "DualThrustStrategy",
    "KingKeltnerStrategy",
    "MultiSignalStrategy",
    "MultiTimeframeStrategy",
    "TurtleSignalStrategy"
])

print(f"[VNPY] 策略模块初始化完成，包含 {len(__all__)} 个策略")
print(f"[VNPY] 可用策略: {__all__}")
'''
    
    # 备份原文件
    backup_file = current_dir / "__init__.py.backup"
    if init_file.exists():
        import shutil
        shutil.copy2(init_file, backup_file)
        print(f"✅ 原__init__.py已备份到: {backup_file}")
    
    # 写入修复版本
    with open(init_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"✅ 修复版__init__.py已创建")
    print("⚠️  请重启VNPY后测试")
    
    return True

def main():
    """主函数"""
    print("🔍 VNPY策略导入问题诊断")
    print(f"诊断时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行诊断
    test1 = test_vnpy_environment()
    if not test1:
        print("\n❌ VNPY环境有问题，请先修复VNPY安装")
        return
    
    test2 = test_strategy_import_in_vnpy()
    test3 = test_direct_strategy_import()
    test4 = check_vnpy_strategy_discovery()
    
    # 如果策略发现失败，创建修复文件
    if not test4:
        print("\n🔧 策略未被VNPY发现，尝试修复...")
        create_fixed_init_file()
        
        print("\n" + "=" * 60)
        print("修复完成")
        print("=" * 60)
        print("📋 下一步操作:")
        print("1. 完全关闭VNPY/VeighNa Studio")
        print("2. 重新启动VNPY/VeighNa Studio") 
        print("3. 进入CTA策略模块")
        print("4. 查找 SignalSystemStrategy")
        print("\n如果仍然看不到，请运行:")
        print("python diagnose_vnpy_import.py")
    else:
        print("\n🎉 策略已被VNPY正确发现！")
        print("如果在界面中仍看不到，请:")
        print("1. 重启VNPY")
        print("2. 检查CTA策略模块是否正确加载")

if __name__ == "__main__":
    main()
