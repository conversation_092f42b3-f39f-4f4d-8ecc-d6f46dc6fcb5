#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试简化策略是否能被VNPY正确识别
"""

import sys
import traceback

def test_simple_strategy_import():
    """测试简化策略导入"""
    print("=" * 60)
    print("简化策略导入测试")
    print("=" * 60)
    
    try:
        # 直接导入策略
        from vnpy_ctastrategy.strategies.simple_signal_strategy import SimpleSignalStrategy
        print("✅ SimpleSignalStrategy 直接导入成功")
        
        # 检查策略属性
        print(f"   作者: {SimpleSignalStrategy.author}")
        print(f"   参数: {SimpleSignalStrategy.parameters}")
        print(f"   变量: {SimpleSignalStrategy.variables}")
        
        return True
        
    except Exception as e:
        print(f"❌ SimpleSignalStrategy 导入失败: {e}")
        traceback.print_exc()
        return False

def test_strategy_module_import():
    """测试策略模块导入"""
    print("\n" + "=" * 60)
    print("策略模块导入测试")
    print("=" * 60)
    
    try:
        # 导入策略模块
        import vnpy_ctastrategy.strategies as strategies_module
        print("✅ 策略模块导入成功")
        
        # 检查__all__
        if hasattr(strategies_module, '__all__'):
            all_strategies = strategies_module.__all__
            print(f"✅ 发现 {len(all_strategies)} 个注册策略")
            
            # 检查我们的策略
            target_strategies = ['SimpleSignalStrategy', 'SignalSystemStrategy', 'SignalSystemDeepLearningStrategy']
            found_strategies = []
            
            for strategy_name in target_strategies:
                if strategy_name in all_strategies:
                    found_strategies.append(strategy_name)
                    print(f"   ✅ {strategy_name} - 已注册")
                else:
                    print(f"   ❌ {strategy_name} - 未注册")
            
            # 尝试获取策略类
            for strategy_name in found_strategies:
                try:
                    strategy_class = getattr(strategies_module, strategy_name, None)
                    if strategy_class:
                        print(f"   ✅ {strategy_name} - 类可获取")
                    else:
                        print(f"   ❌ {strategy_name} - 类不存在")
                except Exception as e:
                    print(f"   ❌ {strategy_name} - 获取失败: {e}")
            
            return len(found_strategies) > 0
        else:
            print("❌ 策略模块没有__all__属性")
            return False
            
    except Exception as e:
        print(f"❌ 策略模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_strategy_instantiation():
    """测试策略实例化"""
    print("\n" + "=" * 60)
    print("策略实例化测试")
    print("=" * 60)
    
    try:
        from vnpy_ctastrategy.strategies.simple_signal_strategy import SimpleSignalStrategy
        
        # 模拟CTA引擎
        class MockCtaEngine:
            def write_log(self, msg):
                print(f"    [策略日志] {msg}")
        
        # 创建策略实例
        strategy = SimpleSignalStrategy(
            cta_engine=MockCtaEngine(),
            strategy_name="test_simple_strategy",
            vt_symbol="rb2501.SHFE",
            setting={
                "confidence_threshold": 0.6,
                "position_size": 1,
                "ma_period": 20
            }
        )
        
        print("✅ 策略实例化成功")
        print(f"   策略名称: {strategy.strategy_name}")
        print(f"   交易品种: {strategy.vt_symbol}")
        print(f"   置信度阈值: {strategy.confidence_threshold}")
        print(f"   MA周期: {strategy.ma_period}")
        
        # 测试策略初始化
        strategy.on_init()
        print("✅ 策略初始化成功")
        
        # 测试策略启动
        strategy.on_start()
        print("✅ 策略启动成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略实例化失败: {e}")
        traceback.print_exc()
        return False

def create_vnpy_usage_instructions():
    """创建VNPY使用说明"""
    print("\n" + "=" * 60)
    print("VNPY使用说明")
    print("=" * 60)
    
    instructions = """
🎯 在VNPY中使用SimpleSignalStrategy的步骤:

1. 🔄 重启VeighNa Studio
   - 完全关闭VeighNa Studio
   - 重新启动应用程序

2. 📊 进入CTA策略模块
   - 点击"CTA策略"选项卡

3. 🔍 查找策略
   - 在策略列表中查找 "SimpleSignalStrategy"
   - 这是最简化、最兼容的版本

4. ⚙️ 配置策略参数
   策略名称: SimpleSignalStrategy
   交易品种: rb2501.SHFE (螺纹钢主力)
   参数配置:
   - confidence_threshold: 0.6 (置信度阈值)
   - position_size: 1 (仓位大小)
   - stop_loss_pct: 0.02 (止损2%)
   - take_profit_pct: 0.04 (止盈4%)
   - ma_period: 20 (移动平均周期)

5. 🚀 启动策略
   - 点击"初始化"按钮
   - 点击"启动"按钮

📋 策略特点:
- ✅ 完全兼容VNPY，无外部依赖
- ✅ 基于移动平均线的趋势跟踪
- ✅ 成交量确认机制
- ✅ 自动止损止盈
- ✅ 防止过度频繁交易

⚠️ 注意事项:
- 首次使用建议在模拟环境测试
- 确保有足够资金和风险承受能力
- 定期检查策略表现
- 根据市场情况调整参数

🔧 如果仍然看不到策略:
1. 检查VNPY版本是否为4.1.0+
2. 确认CTA策略模块已正确加载
3. 查看VNPY启动日志是否有错误
4. 尝试手动刷新策略列表
"""
    
    print(instructions)
    
    # 保存说明到文件
    from pathlib import Path
    instructions_file = Path(__file__).parent / "SIMPLE_STRATEGY_USAGE.md"
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write("# SimpleSignalStrategy 使用说明\n\n")
        f.write(instructions)
    
    print(f"✅ 使用说明已保存到: {instructions_file}")

def main():
    """主函数"""
    print("🚀 简化策略测试")
    print(f"测试时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行测试
    test1 = test_simple_strategy_import()
    test2 = test_strategy_module_import()
    test3 = test_strategy_instantiation()
    
    # 创建使用说明
    create_vnpy_usage_instructions()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    tests = [test1, test2, test3]
    success_count = sum(tests)
    total_tests = len(tests)
    
    print(f"测试通过率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count >= 2:
        print("🎉 SimpleSignalStrategy 已准备就绪！")
        print("📋 策略已成功注册到VNPY系统")
        print("🔄 请重启VeighNa Studio后查找 'SimpleSignalStrategy'")
        print("📖 详细使用说明请查看生成的文档")
    else:
        print("❌ 策略存在问题，请检查错误信息")
    
    print(f"\n测试完成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
