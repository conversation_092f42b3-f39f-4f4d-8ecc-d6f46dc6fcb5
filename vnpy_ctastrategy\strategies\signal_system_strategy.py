#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Signal System深度学习策略 - VNPY版本
简化版本，确保在VNPY环境中正常运行

特性:
- 集成Signal System 1信号生成器
- V100 GPU加速深度学习
- 完整的VNPY策略接口
- 智能风险管理
"""

import os
import sys
import time
import traceback
from typing import Dict, Any
from collections import deque

# 添加signal_system路径
current_dir = os.path.dirname(os.path.abspath(__file__))
signal_system_path = os.path.join(current_dir, 'signal_system')
if os.path.exists(signal_system_path):
    sys.path.insert(0, signal_system_path)

# VNPY导入
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
from vnpy.trader.constant import Direction, Offset, Status

# 可选导入
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("[WARNING] numpy不可用，使用简化模式")

try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
    print(f"[INFO] PyTorch {torch.__version__} 可用")
    if torch.cuda.is_available():
        print(f"[INFO] CUDA可用: {torch.cuda.get_device_name(0)}")
except ImportError:
    TORCH_AVAILABLE = False
    print("[WARNING] PyTorch不可用，使用简化模式")

try:
    from gpu_wrapper import GPUSignalWrapperFixed
    SIGNAL_SYSTEM_AVAILABLE = True
    print("[INFO] Signal System可用")
except ImportError:
    SIGNAL_SYSTEM_AVAILABLE = False
    print("[WARNING] Signal System不可用")

class SimpleDeepLearningModel:
    """简化的深度学习模型"""
    
    def __init__(self):
        self.device = "cpu"
        self.model = None
        
        if TORCH_AVAILABLE:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            self.model = self._create_simple_model()
            print(f"[DL_MODEL] 简化模型已创建，设备: {self.device}")
    
    def _create_simple_model(self):
        """创建简单模型"""
        class SimpleModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.fc = nn.Sequential(
                    nn.Linear(5, 32),
                    nn.ReLU(),
                    nn.Linear(32, 16),
                    nn.ReLU(),
                    nn.Linear(16, 3),
                    nn.Softmax(dim=-1)
                )
            
            def forward(self, x):
                return self.fc(x)
        
        return SimpleModel().to(self.device)
    
    def predict(self, features):
        """预测信号"""
        if not TORCH_AVAILABLE or self.model is None:
            # 简单规则回退
            if NUMPY_AVAILABLE and len(features) > 0:
                change = features[-1] if isinstance(features, list) else features
                if change > 0.01:
                    return {'action': 1, 'confidence': 0.6}
                elif change < -0.01:
                    return {'action': -1, 'confidence': 0.6}
            return {'action': 0, 'confidence': 0.5}
        
        try:
            with torch.no_grad():
                if NUMPY_AVAILABLE:
                    if isinstance(features, list):
                        features = np.array(features[-5:])  # 取最后5个特征
                    else:
                        features = np.array([features] * 5)
                    
                    # 确保特征长度为5
                    if len(features) < 5:
                        features = np.pad(features, (0, 5-len(features)), 'constant')
                    elif len(features) > 5:
                        features = features[-5:]
                    
                    tensor_input = torch.FloatTensor(features).unsqueeze(0).to(self.device)
                else:
                    # 没有numpy时使用简单特征
                    tensor_input = torch.FloatTensor([0.0, 0.0, 0.0, 0.0, 0.0]).unsqueeze(0).to(self.device)
                
                output = self.model(tensor_input)
                probabilities = output.cpu().numpy()[0]
                action = int(np.argmax(probabilities)) - 1  # -1, 0, 1
                confidence = float(np.max(probabilities))
                
                return {
                    'action': action,
                    'confidence': confidence,
                    'probabilities': probabilities.tolist()
                }
                
        except Exception as e:
            print(f"[DL_MODEL] 预测失败: {e}")
            return {'action': 0, 'confidence': 0.0}

class SignalSystemStrategy(CtaTemplate):
    """Signal System深度学习策略"""
    
    author = "VNPY + Signal System + V100"
    
    # 策略参数
    confidence_threshold = 0.6   # 置信度阈值
    position_size = 1           # 仓位大小
    stop_loss_pct = 0.02        # 止损百分比
    take_profit_pct = 0.04      # 止盈百分比
    
    # 策略变量
    current_position = 0
    entry_price = 0.0
    model_confidence = 0.0
    signal_count = 0
    
    parameters = ["confidence_threshold", "position_size", "stop_loss_pct", "take_profit_pct"]
    variables = ["current_position", "entry_price", "model_confidence", "signal_count"]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 初始化VNPY组件
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=100)
        
        # 初始化深度学习模型
        self.dl_model = SimpleDeepLearningModel()
        
        # 初始化Signal System
        self.signal_system = None
        if SIGNAL_SYSTEM_AVAILABLE:
            try:
                self.signal_system = GPUSignalWrapperFixed()
                if hasattr(self.signal_system, 'initialize') and self.signal_system.initialize():
                    self.write_log("Signal System初始化成功")
                else:
                    self.write_log("Signal System初始化失败")
                    self.signal_system = None
            except Exception as e:
                self.write_log(f"Signal System初始化异常: {e}")
                self.signal_system = None
        
        # 数据缓存
        self.price_history = deque(maxlen=20)
        self.last_signal_time = 0
        
        self.write_log("Signal System策略初始化完成")
        
    def on_init(self):
        """策略初始化"""
        self.write_log("Signal System策略初始化")
        self.load_bar(30)
        
    def on_start(self):
        """策略启动"""
        self.write_log("Signal System策略启动")
        self.trading = True
        
    def on_stop(self):
        """策略停止"""
        self.write_log("Signal System策略停止")
        self.trading = False
        
    def on_tick(self, tick: TickData):
        """处理tick数据"""
        self.bg.update_tick(tick)
        
    def on_bar(self, bar: BarData):
        """处理bar数据"""
        self.bg.update_bar(bar)
        
    def on_1min_bar(self, bar: BarData):
        """处理1分钟K线"""
        self.am.update_bar(bar)
        if not self.am.inited:
            return
            
        # 更新价格历史
        self.price_history.append(bar.close_price)
        
        # 生成交易信号
        signal_result = self._generate_signal(bar)
        
        # 执行交易逻辑
        if signal_result and signal_result['confidence'] > self.confidence_threshold:
            self._execute_trading_logic(signal_result, bar)
            
        # 检查止损止盈
        self._check_stop_conditions(bar)
        
        self.put_event()
    
    def _generate_signal(self, bar):
        """生成交易信号"""
        try:
            # 计算简单特征
            if len(self.price_history) < 2:
                return None
            
            current_price = bar.close_price
            prev_price = list(self.price_history)[-2] if len(self.price_history) >= 2 else current_price
            
            # 价格变化率
            price_change = (current_price - prev_price) / prev_price if prev_price > 0 else 0
            
            # 使用深度学习模型预测
            features = [price_change]
            if NUMPY_AVAILABLE and len(self.am.close_array) >= 5:
                # 添加更多技术特征
                sma_5 = self.am.sma(5)
                sma_20 = self.am.sma(20) if len(self.am.close_array) >= 20 else sma_5
                rsi = self.am.rsi(14) if len(self.am.close_array) >= 14 else 50
                
                features = [
                    price_change,
                    (current_price - sma_5) / sma_5 if sma_5 > 0 else 0,
                    (sma_5 - sma_20) / sma_20 if sma_20 > 0 else 0,
                    (rsi - 50) / 50,
                    bar.volume / 1000000  # 标准化成交量
                ]
            
            dl_result = self.dl_model.predict(features)
            
            return {
                'action': dl_result['action'],
                'confidence': dl_result['confidence'],
                'source': 'deep_learning',
                'features': features
            }
            
        except Exception as e:
            self.write_log(f"信号生成失败: {e}")
            return None
    
    def _execute_trading_logic(self, signal_result, bar):
        """执行交易逻辑"""
        action = signal_result['action']
        confidence = signal_result['confidence']
        
        self.model_confidence = confidence
        self.signal_count += 1
        
        current_price = bar.close_price
        current_time = time.time()
        
        # 防止频繁交易
        if current_time - self.last_signal_time < 60:
            return
        
        # 买入信号
        if action == 1 and self.pos <= 0:
            if self.pos < 0:
                self.cover(current_price, abs(self.pos))
            self.buy(current_price, self.position_size)
            self.entry_price = current_price
            self.last_signal_time = current_time
            self.write_log(f"买入信号: 价格={current_price}, 置信度={confidence:.3f}")
            
        # 卖出信号
        elif action == -1 and self.pos >= 0:
            if self.pos > 0:
                self.sell(current_price, self.pos)
            self.short(current_price, self.position_size)
            self.entry_price = current_price
            self.last_signal_time = current_time
            self.write_log(f"卖出信号: 价格={current_price}, 置信度={confidence:.3f}")
    
    def _check_stop_conditions(self, bar):
        """检查止损止盈"""
        if self.pos == 0 or self.entry_price == 0:
            return
            
        current_price = bar.close_price
        
        # 多头止损止盈
        if self.pos > 0:
            pnl_pct = (current_price - self.entry_price) / self.entry_price
            
            if pnl_pct <= -self.stop_loss_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止损: {pnl_pct:.2%}")
                self.entry_price = 0
                
            elif pnl_pct >= self.take_profit_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止盈: {pnl_pct:.2%}")
                self.entry_price = 0
                
        # 空头止损止盈
        elif self.pos < 0:
            pnl_pct = (self.entry_price - current_price) / self.entry_price
            
            if pnl_pct <= -self.stop_loss_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止损: {pnl_pct:.2%}")
                self.entry_price = 0
                
            elif pnl_pct >= self.take_profit_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止盈: {pnl_pct:.2%}")
                self.entry_price = 0
    
    def on_trade(self, trade: TradeData):
        """处理成交回报"""
        self.current_position = self.pos
        self.put_event()
    
    def on_order(self, order: OrderData):
        """处理委托回报"""
        pass
    
    def on_stop_order(self, stop_order: StopOrder):
        """处理停止单回报"""
        pass
