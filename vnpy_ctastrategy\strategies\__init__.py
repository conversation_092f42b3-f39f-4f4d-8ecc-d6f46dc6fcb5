# -*- coding: utf-8 -*-
"""
VNPY策略模块初始化文件
简化版本，避免复杂导入
"""

# 导入策略类 - 优先导入独立策略
try:
    from .independent_signal_strategy import IndependentSignalStrategy
    INDEPENDENT_SIGNAL_AVAILABLE = True
    print("[VNPY] IndependentSignalStrategy 导入成功")
except ImportError as e:
    print(f"[VNPY策略] IndependentSignalStrategy导入失败: {e}")
    INDEPENDENT_SIGNAL_AVAILABLE = False

try:
    from .simple_signal_strategy import SimpleSignalStrategy
    SIMPLE_SIGNAL_AVAILABLE = True
    print("[VNPY] SimpleSignalStrategy 导入成功")
except ImportError as e:
    print(f"[VNPY策略] SimpleSignalStrategy导入失败: {e}")
    SIMPLE_SIGNAL_AVAILABLE = False

try:
    from .signal_system_deep_learning_strategy import SignalSystemDeepLearningStrategy
    SIGNAL_DL_AVAILABLE = True
    print("[VNPY] SignalSystemDeepLearningStrategy 导入成功")
except ImportError as e:
    print(f"[VNPY策略] Signal System深度学习策略导入失败: {e}")
    SIGNAL_DL_AVAILABLE = False

try:
    from .signal_system_strategy import SignalSystemStrategy
    SIGNAL_SIMPLE_AVAILABLE = True
    print("[VNPY] SignalSystemStrategy 导入成功")
except ImportError as e:
    print(f"[VNPY策略] Signal System简化策略导入失败: {e}")
    SIGNAL_SIMPLE_AVAILABLE = False

# 简单的策略列表，让VNPY自动发现
__all__ = []

# 添加我们的策略 (优先级最高)
if INDEPENDENT_SIGNAL_AVAILABLE:
    __all__.append("IndependentSignalStrategy")
if SIMPLE_SIGNAL_AVAILABLE:
    __all__.append("SimpleSignalStrategy")
if SIGNAL_SIMPLE_AVAILABLE:
    __all__.append("SignalSystemStrategy")
if SIGNAL_DL_AVAILABLE:
    __all__.append("SignalSystemDeepLearningStrategy")

# 添加现有策略
__all__.extend([
    "DoubleMaStrategy",
    "AtrRsiStrategy",
    "BollChannelStrategy",
    "DualThrustStrategy",
    "KingKeltnerStrategy",
    "MultiSignalStrategy",
    "MultiTimeframeStrategy",
    "TurtleSignalStrategy"
])

print(f"[VNPY] 策略模块加载完成，包含 {len(__all__)} 个策略")
print(f"[VNPY] 可用策略: {__all__[:5]}...")  # 只显示前5个

print(f"[VNPY策略] 策略模块已初始化，包含 {len(__all__)} 个策略类")