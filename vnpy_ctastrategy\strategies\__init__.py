# -*- coding: utf-8 -*-
"""
VNPY策略模块初始化文件
简化版本，避免复杂导入
"""

# 导入策略类
try:
    from .signal_system_deep_learning_strategy import SignalSystemDeepLearningStrategy
    SIGNAL_DL_AVAILABLE = True
except ImportError as e:
    print(f"[VNPY策略] Signal System深度学习策略导入失败: {e}")
    SIGNAL_DL_AVAILABLE = False

try:
    from .signal_system_strategy import SignalSystemStrategy
    SIGNAL_SIMPLE_AVAILABLE = True
except ImportError as e:
    print(f"[VNPY策略] Signal System简化策略导入失败: {e}")
    SIGNAL_SIMPLE_AVAILABLE = False

# 简单的策略列表，让VNPY自动发现
__all__ = [
    "SignalSystemStrategy",              # 简化版Signal System策略 (推荐)
    "SignalSystemDeepLearningStrategy",  # 完整版深度学习策略
    "GPUSignalVNPYStrategy",
    "VNPYFinalStrategy",
    "V100SignalStrategy",
    "Blessing3TickStrategy",
    "MultiSignalStrategy",
    "KingKeltnerStrategy",
    "DoubleMaStrategy",
    "AtrRsiStrategy",
    "DualThrustStrategy",
    "TurtleSignalStrategy",
    "MultiTimeframeStrategy",
    "BollChannelStrategy",
    "TestSystemStatus"
]

# 过滤掉不可用的策略
if not SIGNAL_DL_AVAILABLE:
    __all__.remove("SignalSystemDeepLearningStrategy")
if not SIGNAL_SIMPLE_AVAILABLE:
    __all__.remove("SignalSystemStrategy")

print(f"[VNPY策略] 策略模块已初始化，包含 {len(__all__)} 个策略类")