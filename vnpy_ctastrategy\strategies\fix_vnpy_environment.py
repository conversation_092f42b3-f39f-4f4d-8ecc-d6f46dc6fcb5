#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNPY环境修复脚本
解决numpy版本兼容性问题
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"执行命令: {command}")
    print("-" * 50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败")
            return False
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def check_current_environment():
    """检查当前环境"""
    print("=" * 60)
    print("当前环境检查")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查关键包
    packages_to_check = ['numpy', 'talib', 'vnpy', 'vnpy_ctastrategy']
    
    for package in packages_to_check:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
        except Exception as e:
            print(f"⚠️  {package}: 导入异常 - {e}")

def fix_numpy_talib_compatibility():
    """修复numpy和talib兼容性"""
    print("\n" + "=" * 60)
    print("修复numpy和talib兼容性")
    print("=" * 60)
    
    steps = [
        ("pip uninstall talib -y", "卸载talib"),
        ("pip uninstall numpy -y", "卸载numpy"),
        ("pip install numpy==1.24.3", "安装兼容版本numpy"),
        ("pip install TA-Lib", "重新安装talib"),
    ]
    
    for command, description in steps:
        success = run_command(command, description)
        if not success:
            print(f"⚠️  {description}失败，继续下一步...")

def reinstall_vnpy():
    """重新安装VNPY"""
    print("\n" + "=" * 60)
    print("重新安装VNPY")
    print("=" * 60)
    
    steps = [
        ("pip uninstall vnpy vnpy-ctastrategy -y", "卸载VNPY"),
        ("pip install vnpy", "安装VNPY核心"),
        ("pip install vnpy-ctastrategy", "安装CTA策略模块"),
    ]
    
    for command, description in steps:
        success = run_command(command, description)
        if not success:
            print(f"⚠️  {description}失败，继续下一步...")

def test_vnpy_import():
    """测试VNPY导入"""
    print("\n" + "=" * 60)
    print("测试VNPY导入")
    print("=" * 60)
    
    test_imports = [
        ("import numpy", "numpy"),
        ("import talib", "talib"),
        ("import vnpy", "vnpy"),
        ("from vnpy_ctastrategy import CtaTemplate", "vnpy_ctastrategy"),
        ("from vnpy.trader.constant import Direction", "vnpy.trader.constant"),
    ]
    
    success_count = 0
    
    for import_code, package_name in test_imports:
        try:
            exec(import_code)
            print(f"✅ {package_name}: 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {package_name}: 导入失败 - {e}")
    
    success_rate = success_count / len(test_imports) * 100
    print(f"\n导入成功率: {success_count}/{len(test_imports)} ({success_rate:.1f}%)")
    
    return success_rate >= 80

def create_alternative_solution():
    """创建替代解决方案"""
    print("\n" + "=" * 60)
    print("创建替代解决方案")
    print("=" * 60)
    
    print("如果环境修复失败，可以使用以下替代方案:")
    print()
    print("方案1: 使用conda环境")
    print("conda create -n vnpy_env python=3.9")
    print("conda activate vnpy_env")
    print("conda install numpy=1.24.3")
    print("conda install -c conda-forge ta-lib")
    print("pip install vnpy vnpy-ctastrategy")
    print()
    print("方案2: 使用Docker")
    print("docker pull vnpy/vnpy:latest")
    print("docker run -it vnpy/vnpy:latest")
    print()
    print("方案3: 使用超简化策略")
    print("- 策略名称: UltraSimpleStrategy")
    print("- 不依赖talib，纯Python实现")
    print("- 基于双均线交叉策略")
    print("- 完全兼容任何VNPY环境")

def main():
    """主函数"""
    print("🔧 VNPY环境修复工具")
    print("解决numpy版本兼容性问题")
    print("=" * 60)
    
    # 检查当前环境
    check_current_environment()
    
    # 询问用户是否继续
    print("\n" + "=" * 60)
    print("修复选项")
    print("=" * 60)
    print("1. 自动修复环境 (推荐)")
    print("2. 仅测试导入")
    print("3. 显示替代方案")
    print("4. 退出")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
    except KeyboardInterrupt:
        print("\n用户取消操作")
        return
    
    if choice == "1":
        print("\n开始自动修复环境...")
        
        # 修复numpy和talib兼容性
        fix_numpy_talib_compatibility()
        
        # 重新安装VNPY
        reinstall_vnpy()
        
        # 测试导入
        if test_vnpy_import():
            print("\n🎉 环境修复成功！")
            print("请重启VeighNa Studio并查找以下策略:")
            print("- UltraSimpleStrategy (推荐)")
            print("- IndependentSignalStrategy")
        else:
            print("\n⚠️  环境修复未完全成功")
            create_alternative_solution()
    
    elif choice == "2":
        test_vnpy_import()
    
    elif choice == "3":
        create_alternative_solution()
    
    elif choice == "4":
        print("退出修复工具")
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
