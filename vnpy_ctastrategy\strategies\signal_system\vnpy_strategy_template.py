#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于signal_system_1信号生成器的VeighNa多商品期货交易策略
支持为不同商品自动加载对应模型
"""

import os
import sys
import time
import re
import glob
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Callable, Any, Tuple

# 导入VeighNa组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 添加signal_system_1目录到Python路径
# 注意：请根据实际安装位置修改此路径
signal_system_path = os.path.dirname(os.path.abspath(__file__))
if signal_system_path not in sys.path:
    sys.path.append(signal_system_path)

# 导入signal_system_1信号生成器
try:
    from signal_generator import SignalSystemAPI
    SIGNAL_SYSTEM_AVAILABLE = True
except ImportError:
    print("警告：无法导入信号系统，请检查signal_generator.py文件是否存在")
    SIGNAL_SYSTEM_AVAILABLE = False
    
# 商品代码提取正则表达式
SYMBOL_PATTERN = re.compile(r'([A-Za-z]+)(\d+)')

class VnpySignalStrategy(CtaTemplate):
    """
    基于signal_system_1信号生成器的VeighNa多商品期货交易策略
    
    策略逻辑：
    1. 使用signal_system_1的信号生成器产生交易信号
    2. 根据信号方向和置信度决定交易方向和仓位大小
    3. 实现止损和止盈逻辑
    4. 支持在线学习和模型更新
    5. 支持多商品期货交易，自动加载对应商品模型
    
    适用于VeighNa量化交易框架v3.0及以上版本
    """
    
    author = "Signal System"
    
    # 模型目录设置
    models_dir = "models"  # 模型存放目录，相对于策略目录
    
    # 策略参数
    signal_threshold = 0.6       # 信号阈值，高于此值才执行交易
    position_ratio = 0.3         # 仓位比例系数
    stop_loss_ratio = 0.02       # 止损比例
    take_profit_ratio = 0.05     # 止盈比例
    trailing_stop = True         # 是否启用追踪止损
    trailing_ratio = 0.5         # 追踪止损比例
    use_online_learning = True   # 是否使用在线学习
    learning_interval = 20       # 学习间隔（K线数）
    auto_switch_model = True     # 是否自动切换合约月份对应的模型
    use_product_model = True     # 当没有具体合约模型时，是否使用品种通用模型
    
    # 变量
    signal_value = 0             # 当前信号值 (-1, 0, 1)
    signal_confidence = 0.0      # 当前信号置信度
    entry_price = 0.0            # 入场价格
    high_since_entry = 0.0       # 入场后最高价
    low_since_entry = 0.0        # 入场后最低价
    bar_count = 0                # K线计数
    features = []                # 特征数据
    product_code = ""            # 商品代码（如FG）
    contract_code = ""           # 合约代码（如509）
    current_model_path = ""      # 当前使用的模型路径
    
    # 参数列表，用于GUI显示
    parameters = [
        "signal_threshold",
        "position_ratio",
        "stop_loss_ratio",
        "take_profit_ratio",
        "trailing_stop",
        "trailing_ratio",
        "use_online_learning",
        "learning_interval",
        "auto_switch_model",
        "use_product_model"
    ]
    
    # 变量列表，用于GUI显示
    variables = [
        "signal_value",
        "signal_confidence",
        "entry_price",
        "high_since_entry",
        "low_since_entry",
        "bar_count",
        "product_code",
        "contract_code",
        "current_model_path"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """
        初始化策略
        """
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建K线生成器
        self.bg = BarGenerator(self.on_bar)
        
        # 创建技术指标计算器
        self.am = ArrayManager(size=100)
        
        # 解析交易品种和合约代码
        self.parse_symbol(vt_symbol)
        
        # 初始化信号系统
        if SIGNAL_SYSTEM_AVAILABLE:
            try:
                # 查找并加载适合当前合约的模型
                model_path = self.find_appropriate_model()
                if model_path:
                    self.current_model_path = model_path
                    self.write_log(f"使用模型: {model_path}")
                    
                    # 初始化信号系统API并加载模型
                    self.signal_api = SignalSystemAPI()
                    if hasattr(self.signal_api, 'load_model'):
                        self.signal_api.load_model(model_path)
                        
                    if not self.signal_api.is_initialized():
                        self.write_log("信号系统初始化失败")
                    else:
                        self.write_log("信号系统初始化成功")
                else:
                    self.write_log(f"未找到适合{self.product_code}{self.contract_code}的模型")
                    self.signal_api = None
            except Exception as e:
                self.write_log(f"信号系统初始化异常: {e}")
                self.signal_api = None
        else:
            self.signal_api = None
            self.write_log("信号系统模块不可用，策略将无法正常运行")
        
        # 初始化特征缓存
        self.features_cache = []
        
        # 记录上次信号更新时间
        self.last_signal_time = None
        
        # 记录交易状态
        self.trading_status = {
            "long_entered": False,
            "short_entered": False,
            "last_direction": None,
            "position_holding_bars": 0
        }
        
        # 记录资金
        self.capital = 1000000  # 默认资金，可通过setting参数传入
        
        # 模型管理
        self.model_cache = {}  # 缓存已加载的模型 {symbol: model_path}
        self.last_model_check = datetime.now()  # 上次检查模型更新时间
    
    def parse_symbol(self, vt_symbol):
        """
        解析交易品种和合约代码
        例如：FG509.SHFE 分解为品种=FG, 合约代码=509
        """
        try:
            # 先分离交易所代码
            symbol = vt_symbol.split('.')[0]
            
            # 使用正则表达式提取品种和合约代码
            match = SYMBOL_PATTERN.match(symbol)
            if match:
                self.product_code = match.group(1)  # 品种代码，如FG
                self.contract_code = match.group(2)  # 合约代码，如509
                self.write_log(f"解析合约: 品种={self.product_code}, 合约代码={self.contract_code}")
            else:
                self.write_log(f"无法解析合约代码: {symbol}")
                self.product_code = symbol  # 如果无法解析，则使用原始符号
                self.contract_code = ""
        except Exception as e:
            self.write_log(f"解析合约代码异常: {e}")
            self.product_code = vt_symbol.split('.')[0]  # 默认使用点前的全部内容
            self.contract_code = ""
    
    def find_appropriate_model(self):
        """
        查找适合当前合约的模型
        优先级：
        1. 精确匹配的合约模型（如FG509.bin）
        2. 如果开启了use_product_model，则使用品种通用模型（如FG.bin）
        3. 默认模型（model.bin）
        """
        if not self.product_code:
            self.write_log("商品代码为空，无法查找模型")
            return None
            
        # 构建模型目录路径
        model_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), self.models_dir)
        if not os.path.exists(model_dir):
            os.makedirs(model_dir, exist_ok=True)
            self.write_log(f"创建模型目录: {model_dir}")
        
        # 尝试查找精确匹配的合约模型
        contract_model_path = os.path.join(model_dir, f"{self.product_code}{self.contract_code}.bin")
        if os.path.exists(contract_model_path):
            self.write_log(f"找到精确匹配的合约模型: {contract_model_path}")
            return contract_model_path
        
        # 如果开启了use_product_model，尝试查找品种通用模型
        if self.use_product_model:
            product_model_path = os.path.join(model_dir, f"{self.product_code}.bin")
            if os.path.exists(product_model_path):
                self.write_log(f"找到品种通用模型: {product_model_path}")
                return product_model_path
        
        # 尝试使用默认模型
        default_model_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.bin")
        if os.path.exists(default_model_path):
            self.write_log(f"使用默认模型: {default_model_path}")
            return default_model_path
        
        self.write_log("未找到适用的模型文件")
        return None
        
    def check_model_update(self):
        """
        检查并更新模型，每天检查一次
        """
        now = datetime.now()
        
        # 每天只检查一次
        if (now - self.last_model_check).days < 1 and not self.auto_switch_model:
            return
            
        self.last_model_check = now
        self.write_log("检查模型更新")
        
        # 查找适合的模型
        new_model_path = self.find_appropriate_model()
        
        # 如果找到新模型且与当前使用的不同
        if new_model_path and new_model_path != self.current_model_path:
            self.write_log(f"发现新模型: {new_model_path}, 切换中...")
            
            try:
                # 如果信号系统支持加载模型
                if self.signal_api and hasattr(self.signal_api, 'load_model'):
                    self.signal_api.load_model(new_model_path)
                    self.current_model_path = new_model_path
                    self.write_log(f"模型切换成功: {new_model_path}")
                else:
                    # 如果不支持加载模型，需要重新初始化信号系统
                    if SIGNAL_SYSTEM_AVAILABLE:
                        self.signal_api = SignalSystemAPI()
                        self.current_model_path = new_model_path
                        self.write_log(f"重新初始化信号系统并切换模型: {new_model_path}")
            except Exception as e:
                self.write_log(f"切换模型失败: {e}")
    
    def on_init(self):
        """
        策略初始化完成回调
        """
        self.write_log("策略初始化完成")
        
        # 检查信号系统是否可用
        if not self.signal_api:
            self.write_log("警告：信号系统不可用，策略将无法正常运行")
            return
            
        # 加载历史数据
        self.write_log("开始加载历史数据")
        self.load_bar(10)  # 加载10天历史数据
    
    def on_start(self):
        """
        策略启动回调
        """
        self.write_log("策略启动")
    
    def on_stop(self):
        """
        策略停止回调
        """
        self.write_log("策略停止")
    
    def on_tick(self, tick: TickData):
        """
        Tick数据回调
        """
        self.bg.update_tick(tick)
    
    def on_bar(self, bar: BarData):
        """
        K线数据回调
        """
        # 更新K线计数
        self.bar_count += 1
        
        # 检查是否需要更新模型
        if self.auto_switch_model:
            self.check_model_update()
        
        # 如果信号系统不可用，则跳过
        if not self.signal_api:
            self.write_log("信号系统不可用，跳过处理")
            return
        
        # 更新技术指标
        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return
        
        # 提取特征
        features = self.extract_features(bar, am)
        self.features_cache.append(features)
        
        # 保持特征缓存大小
        if len(self.features_cache) > 100:
            self.features_cache.pop(0)
        
        # 获取信号
        self.signal_value, self.signal_confidence = self.get_signal(features)
        
        # 记录信号
        signal_str = "多头" if self.signal_value == 1 else "空头" if self.signal_value == -1 else "中性"
        self.write_log(f"[{self.product_code}{self.contract_code}] 信号: {signal_str}, 置信度: {self.signal_confidence:.4f}")
        
        # 更新入场后的最高/最低价
        if self.pos > 0:
            self.high_since_entry = max(self.high_since_entry, bar.high_price)
            self.trading_status["position_holding_bars"] += 1
        elif self.pos < 0:
            self.low_since_entry = min(self.low_since_entry, bar.low_price)
            self.trading_status["position_holding_bars"] += 1
        
        # 交易逻辑
        self.trade_logic(bar)
        
        # 在线学习
        if self.use_online_learning and self.bar_count % self.learning_interval == 0:
            self.online_learning()
        
        # 发出策略状态通知
        self.put_event()
    
    def extract_features(self, bar: BarData, am: ArrayManager) -> List[float]:
        """
        从K线数据中提取特征
        """
        # 价格特征
        close = bar.close_price
        open_price = bar.open_price
        high = bar.high_price
        low = bar.low_price
        volume = bar.volume
        
        # 计算基础指标
        if am.inited:
            sma5 = am.sma(5)
            sma10 = am.sma(10)
            sma20 = am.sma(20)
            rsi = am.rsi(14)
            atr = am.atr(14)
            macd, signal, hist = am.macd(12, 26, 9)
            boll_up, boll_mid, boll_down = am.boll(20, 2)
            
            # 计算价格动量
            momentum = close / am.close_array[-5] - 1
            
            # 计算波动率
            volatility = atr / close
            
            # 计算布林带位置
            bb_position = (close - boll_down) / (boll_up - boll_down) if (boll_up - boll_down) > 0 else 0.5
            
            # 计算成交量变化
            volume_change = volume / am.volume_array[-5] - 1 if am.volume_array[-5] > 0 else 0
            
            # 构建特征向量 (与signal_system_1特征保持一致)
            features = [
                (close / open_price - 1),                # 当前K线涨跌幅
                (high / low - 1),                        # 当前K线振幅
                (close / sma5 - 1),                      # 收盘价相对SMA5偏离
                (close / sma20 - 1),                     # 收盘价相对SMA20偏离
                momentum,                                # 价格动量
                volatility,                              # 波动率
                (rsi - 50) / 50,                         # 归一化RSI
                hist,                                    # MACD柱状图
                bb_position,                             # 布林带位置
                volume_change,                           # 成交量变化
                (sma5 / sma20 - 1),                      # 均线差值比率
                (macd / close if close > 0 else 0)       # 归一化MACD
            ]
        else:
            # 如果技术指标未初始化，使用默认值
            features = [0.0] * 12
        
        return features
    
    def get_signal(self, features: List[float]) -> Tuple[int, float]:
        """
        获取信号系统的交易信号
        """
        if not self.signal_api or not self.signal_api.is_initialized():
            self.write_log("信号系统不可用，返回中性信号")
            return 0, 0.0
        
        try:
            # 调用信号系统API获取预测
            signal, confidence = self.signal_api.predict(features)
            
            # 记录信号更新时间
            self.last_signal_time = datetime.now()
            
            return signal, confidence
        except Exception as e:
            self.write_log(f"获取信号异常: {e}")
            return 0, 0.0
    
    def trade_logic(self, bar: BarData):
        """
        交易逻辑
        """
        # 获取当前持仓
        current_pos = self.pos
        
        # 计算目标仓位
        target_pos = 0
        if abs(self.signal_confidence) >= self.signal_threshold:
            # 信号强度超过阈值，计算目标仓位
            position_size = int(self.capital * self.position_ratio * self.signal_confidence / bar.close_price)
            target_pos = position_size if self.signal_value == 1 else -position_size if self.signal_value == -1 else 0
        
        # 止损和止盈逻辑
        if current_pos > 0:
            # 多头止损
            stop_price = self.entry_price * (1 - self.stop_loss_ratio)
            if self.trailing_stop:
                # 追踪止损
                trail_stop_price = self.high_since_entry * (1 - self.trailing_ratio * self.stop_loss_ratio)
                stop_price = max(stop_price, trail_stop_price)
            
            # 多头止盈
            take_profit_price = self.entry_price * (1 + self.take_profit_ratio)
            
            # 检查是否触发止损或止盈
            if bar.low_price <= stop_price or bar.high_price >= take_profit_price:
                target_pos = 0
                self.write_log(f"多头{'止损' if bar.low_price <= stop_price else '止盈'} 触发价格: {stop_price if bar.low_price <= stop_price else take_profit_price}")
        
        elif current_pos < 0:
            # 空头止损
            stop_price = self.entry_price * (1 + self.stop_loss_ratio)
            if self.trailing_stop:
                # 追踪止损
                trail_stop_price = self.low_since_entry * (1 + self.trailing_ratio * self.stop_loss_ratio)
                stop_price = min(stop_price, trail_stop_price) if self.low_since_entry > 0 else stop_price
            
            # 空头止盈
            take_profit_price = self.entry_price * (1 - self.take_profit_ratio)
            
            # 检查是否触发止损或止盈
            if bar.high_price >= stop_price or bar.low_price <= take_profit_price:
                target_pos = 0
                self.write_log(f"空头{'止损' if bar.high_price >= stop_price else '止盈'} 触发价格: {stop_price if bar.high_price >= stop_price else take_profit_price}")
        
        # 执行交易
        if target_pos > current_pos:
            # 开多或加仓
            self.buy(bar.close_price, abs(target_pos - current_pos))
            if current_pos <= 0:
                # 新开仓，记录入场价格
                self.entry_price = bar.close_price
                self.high_since_entry = bar.high_price
                self.trading_status["long_entered"] = True
                self.trading_status["short_entered"] = False
                self.trading_status["last_direction"] = "long"
                self.trading_status["position_holding_bars"] = 0
                self.write_log(f"开多仓 价格: {bar.close_price}, 数量: {abs(target_pos - current_pos)}")
        
        elif target_pos < current_pos:
            # 开空或加仓
            self.sell(bar.close_price, abs(current_pos - target_pos))
            if current_pos >= 0:
                # 新开仓，记录入场价格
                self.entry_price = bar.close_price
                self.low_since_entry = bar.low_price
                self.trading_status["long_entered"] = False
                self.trading_status["short_entered"] = True
                self.trading_status["last_direction"] = "short"
                self.trading_status["position_holding_bars"] = 0
                self.write_log(f"开空仓 价格: {bar.close_price}, 数量: {abs(current_pos - target_pos)}")
    
    def online_learning(self):
        """
        在线学习逻辑
        """
        if not self.use_online_learning or len(self.features_cache) < 20:
            return
        
        if not self.signal_api or not self.am.inited:
            return
        
        try:
            # 获取历史特征和结果
            features_history = self.features_cache[-20:]
            
            # 简单的标签生成逻辑（实际应用中可能需要更复杂的标签生成）
            # 这里假设最近5根K线的价格变动方向作为标签
            if len(self.am.close_array) >= 5:
                if self.am.close_array[-1] > self.am.close_array[-5]:
                    label = 1  # 上涨
                    confidence = min(abs(self.am.close_array[-1] / self.am.close_array[-5] - 1) * 10, 0.9)
                elif self.am.close_array[-1] < self.am.close_array[-5]:
                    label = -1  # 下跌
                    confidence = min(abs(self.am.close_array[-1] / self.am.close_array[-5] - 1) * 10, 0.9)
                else:
                    label = 0  # 横盘
                    confidence = 0.5
                
                # 调用信号系统的在线学习接口
                if hasattr(self.signal_api, 'online_train'):
                    try:
                        self.signal_api.online_train(features_history, label, confidence)
                        self.write_log(f"在线学习完成，标签: {label}, 置信度: {confidence:.4f}")
                    except Exception as e:
                        self.write_log(f"在线学习失败: {e}")
            else:
                self.write_log("数据不足，无法进行在线学习")
        except Exception as e:
            self.write_log(f"在线学习过程异常: {e}")
    
    def on_order(self, order: OrderData):
        """
        订单状态更新回调
        """
        pass
    
    def on_trade(self, trade: TradeData):
        """
        成交更新回调
        """
        # 更新策略持仓
        self.pos = self.get_pos()
        self.put_event()
        
        # 记录交易日志
        self.write_log(f"交易执行: 方向={trade.direction}, 开平={trade.offset}, 价格={trade.price}, 数量={trade.volume}")
    
    def on_stop_order(self, stop_order: StopOrder):
        """
        停止单状态更新回调
        """
        pass
