#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Signal System 1 综合测试脚本
检查编译状态、V100优化、VNPY兼容性和深度学习集成
"""

import os
import sys
import time
import traceback
from pathlib import Path

print("=" * 80)
print("Signal System 1 综合测试报告")
print("=" * 80)

# 1. 基础环境检查
print("\n1. 基础环境检查")
print("-" * 40)
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")
print(f"脚本目录: {os.path.dirname(os.path.abspath(__file__))}")

# 2. 文件存在性检查
print("\n2. 关键文件检查")
print("-" * 40)

base_dir = Path(__file__).parent
key_files = {
    "signal_system.dll": base_dir / "python" / "signal_system.dll",
    "signal_system.pyd": base_dir / "python" / "signal_system.pyd", 
    "gpu_wrapper_fixed": base_dir / "python" / "gpu_signal_wrapper_fixed.py",
    "vnpy_strategy": base_dir / "vnpy_signal_strategy.py",
    "compiled_dll": base_dir / "build" / "bin" / "Release" / "signal_system.dll"
}

for name, path in key_files.items():
    exists = path.exists()
    size = path.stat().st_size if exists else 0
    print(f"  {name:20}: {'✅' if exists else '❌'} ({size:,} bytes)")

# 3. PyTorch和CUDA检查
print("\n3. PyTorch和CUDA环境检查")
print("-" * 40)

try:
    import torch
    print(f"  PyTorch版本: ✅ {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"  CUDA可用: ✅ {torch.version.cuda}")
        print(f"  GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"    GPU {i}: {gpu_name} ({memory:.1f}GB)")
            
            # 检查是否为V100
            if "V100" in gpu_name:
                print(f"    🚀 检测到Tesla V100 GPU - 优化已启用")
    else:
        print("  CUDA可用: ❌ 未检测到CUDA支持")
        
except ImportError:
    print("  PyTorch: ❌ 未安装")

# 4. VNPY组件检查
print("\n4. VNPY组件检查")
print("-" * 40)

try:
    from vnpy_ctastrategy import CtaTemplate, BarGenerator, ArrayManager
    print("  VNPY CTA策略: ✅ 导入成功")
    
    from vnpy.trader.constant import Direction, Offset, Status
    print("  VNPY交易常量: ✅ 导入成功")
    
except ImportError as e:
    print(f"  VNPY组件: ❌ 导入失败 - {e}")

# 5. 信号系统功能测试
print("\n5. 信号系统功能测试")
print("-" * 40)

try:
    # 测试GPU包装器
    sys.path.insert(0, str(base_dir / "python"))
    from gpu_signal_wrapper_fixed import GPUSignalWrapperFixed
    
    wrapper = GPUSignalWrapperFixed()
    if wrapper.initialize():
        print("  GPU信号包装器: ✅ 初始化成功")
        
        # 测试信号生成
        test_data = {
            'close': 3500.0,
            'volume': 1000000,
            'high': 3520.0,
            'low': 3480.0,
            'open': 3490.0
        }
        
        result = wrapper.generate_signal(test_data)
        if 'signal' in result:
            print(f"  信号生成测试: ✅ 信号={result['signal']}, 置信度={result.get('confidence', 0):.3f}")
            print(f"  处理时间: {result.get('processing_time', 0)*1000:.2f}ms")
            print(f"  使用设备: {result.get('device', 'unknown')}")
        else:
            print("  信号生成测试: ❌ 未返回有效信号")
            
        # 获取性能统计
        stats = wrapper.get_performance_stats()
        print(f"  性能统计: GPU可用={stats.get('gpu_available', False)}")
        
    else:
        print("  GPU信号包装器: ❌ 初始化失败")
        
except Exception as e:
    print(f"  信号系统测试: ❌ 异常 - {e}")
    traceback.print_exc()

# 6. DLL直接调用测试
print("\n6. DLL直接调用测试")
print("-" * 40)

try:
    import ctypes
    dll_path = base_dir / "build" / "bin" / "Release" / "signal_system.dll"
    
    if dll_path.exists():
        lib = ctypes.WinDLL(str(dll_path))
        print("  DLL加载: ✅ 成功")
        
        # 尝试调用一些基本函数
        try:
            # 这里需要根据实际的DLL导出函数来调整
            print("  DLL函数调用: 需要根据实际导出函数进行测试")
        except Exception as e:
            print(f"  DLL函数调用: ⚠️  {e}")
    else:
        print("  DLL加载: ❌ 文件不存在")
        
except Exception as e:
    print(f"  DLL测试: ❌ 异常 - {e}")

print("\n测试完成 - 第一部分")
