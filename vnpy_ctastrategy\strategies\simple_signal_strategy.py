#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化Signal System策略 - 完全兼容VNPY
确保在任何VNPY环境中都能正常工作
"""

from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
from vnpy.trader.constant import Direction, Offset, Status

class SimpleSignalStrategy(CtaTemplate):
    """简化Signal System策略"""
    
    author = "VNPY + Signal System"
    
    # 策略参数
    confidence_threshold = 0.6   # 置信度阈值
    position_size = 1           # 仓位大小
    stop_loss_pct = 0.02        # 止损百分比
    take_profit_pct = 0.04      # 止盈百分比
    ma_period = 20              # 移动平均周期
    
    # 策略变量
    current_position = 0
    entry_price = 0.0
    signal_confidence = 0.0
    signal_count = 0
    ma_value = 0.0
    
    parameters = [
        "confidence_threshold", 
        "position_size", 
        "stop_loss_pct", 
        "take_profit_pct",
        "ma_period"
    ]
    variables = [
        "current_position", 
        "entry_price", 
        "signal_confidence", 
        "signal_count",
        "ma_value"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 初始化VNPY组件
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=100)
        
        # 简单的信号生成逻辑
        self.price_history = []
        self.last_signal_time = 0
        
        self.write_log("简化Signal System策略初始化完成")
        
    def on_init(self):
        """策略初始化"""
        self.write_log("简化Signal System策略初始化")
        self.load_bar(30)
        
    def on_start(self):
        """策略启动"""
        self.write_log("简化Signal System策略启动")
        self.trading = True
        
    def on_stop(self):
        """策略停止"""
        self.write_log("简化Signal System策略停止")
        self.trading = False
        
    def on_tick(self, tick: TickData):
        """处理tick数据"""
        self.bg.update_tick(tick)
        
    def on_bar(self, bar: BarData):
        """处理bar数据"""
        self.bg.update_bar(bar)
        
    def on_1min_bar(self, bar: BarData):
        """处理1分钟K线"""
        self.am.update_bar(bar)
        if not self.am.inited:
            return
            
        # 更新价格历史
        self.price_history.append(bar.close_price)
        if len(self.price_history) > 50:
            self.price_history.pop(0)
        
        # 计算移动平均
        if len(self.am.close_array) >= self.ma_period:
            self.ma_value = self.am.sma(self.ma_period)
        
        # 生成交易信号
        signal_result = self._generate_simple_signal(bar)
        
        # 执行交易逻辑
        if signal_result and signal_result['confidence'] > self.confidence_threshold:
            self._execute_trading_logic(signal_result, bar)
            
        # 检查止损止盈
        self._check_stop_conditions(bar)
        
        self.put_event()
    
    def _generate_simple_signal(self, bar):
        """生成简单交易信号"""
        try:
            if len(self.price_history) < 10 or self.ma_value == 0:
                return None
            
            current_price = bar.close_price
            
            # 简单的趋势跟踪信号
            # 价格突破移动平均线
            price_ma_ratio = (current_price - self.ma_value) / self.ma_value
            
            # 计算价格动量
            if len(self.price_history) >= 5:
                recent_prices = self.price_history[-5:]
                price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            else:
                price_change = 0
            
            # 成交量确认
            volume_ma = self.am.sma(10, array=True)
            if len(volume_ma) > 0:
                volume_ratio = bar.volume / volume_ma[-1] if volume_ma[-1] > 0 else 1
            else:
                volume_ratio = 1
            
            # 信号生成逻辑
            signal = 0
            confidence = 0.5
            
            # 买入信号：价格上穿MA且有动量确认
            if price_ma_ratio > 0.005 and price_change > 0.01 and volume_ratio > 1.2:
                signal = 1
                confidence = min(0.8, 0.5 + abs(price_ma_ratio) * 10 + abs(price_change) * 5)
                
            # 卖出信号：价格下穿MA且有动量确认
            elif price_ma_ratio < -0.005 and price_change < -0.01 and volume_ratio > 1.2:
                signal = -1
                confidence = min(0.8, 0.5 + abs(price_ma_ratio) * 10 + abs(price_change) * 5)
            
            return {
                'action': signal,
                'confidence': confidence,
                'price_ma_ratio': price_ma_ratio,
                'price_change': price_change,
                'volume_ratio': volume_ratio
            }
            
        except Exception as e:
            self.write_log(f"信号生成失败: {e}")
            return None
    
    def _execute_trading_logic(self, signal_result, bar):
        """执行交易逻辑"""
        action = signal_result['action']
        confidence = signal_result['confidence']
        
        self.signal_confidence = confidence
        self.signal_count += 1
        
        current_price = bar.close_price
        import time
        current_time = time.time()
        
        # 防止频繁交易 - 至少间隔60秒
        if current_time - self.last_signal_time < 60:
            return
        
        # 买入信号
        if action == 1 and self.pos <= 0:
            if self.pos < 0:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"平空仓: 价格={current_price}")
            
            self.buy(current_price, self.position_size)
            self.entry_price = current_price
            self.last_signal_time = current_time
            
            self.write_log(f"买入信号执行: 价格={current_price}, 置信度={confidence:.3f}")
            self.write_log(f"  价格MA比率={signal_result['price_ma_ratio']:.4f}")
            self.write_log(f"  价格变化={signal_result['price_change']:.4f}")
            
        # 卖出信号
        elif action == -1 and self.pos >= 0:
            if self.pos > 0:
                self.sell(current_price, self.pos)
                self.write_log(f"平多仓: 价格={current_price}")
            
            self.short(current_price, self.position_size)
            self.entry_price = current_price
            self.last_signal_time = current_time
            
            self.write_log(f"卖出信号执行: 价格={current_price}, 置信度={confidence:.3f}")
            self.write_log(f"  价格MA比率={signal_result['price_ma_ratio']:.4f}")
            self.write_log(f"  价格变化={signal_result['price_change']:.4f}")
    
    def _check_stop_conditions(self, bar):
        """检查止损止盈条件"""
        if self.pos == 0 or self.entry_price == 0:
            return
            
        current_price = bar.close_price
        
        # 多头止损止盈
        if self.pos > 0:
            pnl_pct = (current_price - self.entry_price) / self.entry_price
            
            if pnl_pct <= -self.stop_loss_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止损: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0
                
            elif pnl_pct >= self.take_profit_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止盈: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0
                
        # 空头止损止盈
        elif self.pos < 0:
            pnl_pct = (self.entry_price - current_price) / self.entry_price
            
            if pnl_pct <= -self.stop_loss_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止损: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0
                
            elif pnl_pct >= self.take_profit_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止盈: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0
    
    def on_trade(self, trade: TradeData):
        """处理成交回报"""
        self.current_position = self.pos
        self.put_event()
    
    def on_order(self, order: OrderData):
        """处理委托回报"""
        pass
    
    def on_stop_order(self, stop_order: StopOrder):
        """处理停止单回报"""
        pass
