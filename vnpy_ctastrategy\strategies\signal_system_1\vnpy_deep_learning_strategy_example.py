#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNPY + Signal System 1 + V100 GPU 深度学习策略示例
展示如何将编译好的信号生成器与VNPY深度学习机制结合
"""

import os
import sys
import time
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any
from collections import deque

# 添加信号系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'python'))

# VNPY核心导入
try:
    from vnpy_ctastrategy import (
        CtaTemplate,
        StopOrder,
        TickData,
        BarData,
        TradeData,
        OrderData,
        BarGenerator,
        ArrayManager,
    )
    from vnpy.trader.constant import Direction, Offset, Status
    VNPY_AVAILABLE = True
except ImportError:
    print("[WARNING] VNPY模块未找到，使用模拟模式")
    VNPY_AVAILABLE = False
    
    # 模拟VNPY类
    class CtaTemplate:
        def __init__(self, cta_engine=None, strategy_name="", vt_symbol="", setting=None):
            self.pos = 0
            self.trading = False
            self.cta_engine = cta_engine
            self.strategy_name = strategy_name
            self.vt_symbol = vt_symbol
            
        def write_log(self, msg): print(f"[{self.strategy_name}] {msg}")
        def buy(self, price, volume, stop=False): self.pos += volume
        def sell(self, price, volume, stop=False): self.pos -= volume
        def cover(self, price, volume, stop=False): self.pos += volume
        def short(self, price, volume, stop=False): self.pos -= volume
        def cancel_all(self): pass
        def put_event(self): pass
        def load_bar(self, days): pass

# 深度学习和GPU组件导入
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
    print(f"[INFO] PyTorch {torch.__version__} 可用")
    
    if torch.cuda.is_available():
        print(f"[INFO] CUDA {torch.version.cuda} 可用，GPU: {torch.cuda.get_device_name(0)}")
    else:
        print("[WARNING] CUDA不可用，将使用CPU模式")
        
except ImportError:
    print("[WARNING] PyTorch不可用，使用简化模式")
    TORCH_AVAILABLE = False

# 信号系统导入
try:
    from gpu_signal_wrapper_fixed import GPUSignalWrapperFixed
    SIGNAL_SYSTEM_AVAILABLE = True
    print("[INFO] Signal System 1 GPU包装器可用")
except ImportError:
    print("[WARNING] Signal System 1 不可用，使用模拟信号")
    SIGNAL_SYSTEM_AVAILABLE = False

class DeepLearningTradingModel:
    """深度学习交易模型"""
    
    def __init__(self, input_size=20, hidden_size=128, output_size=3):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        
        if TORCH_AVAILABLE:
            self.model = self._create_model(input_size, hidden_size, output_size)
            print(f"[DL_MODEL] 模型已创建，设备: {self.device}")
        
    def _create_model(self, input_size, hidden_size, output_size):
        """创建深度学习模型"""
        class TradingLSTM(nn.Module):
            def __init__(self, input_size, hidden_size, output_size):
                super(TradingLSTM, self).__init__()
                self.hidden_size = hidden_size
                
                # LSTM层
                self.lstm = nn.LSTM(input_size, hidden_size, num_layers=2, 
                                  batch_first=True, dropout=0.2)
                
                # 注意力机制
                self.attention = nn.MultiheadAttention(hidden_size, num_heads=8)
                
                # 分类器
                self.classifier = nn.Sequential(
                    nn.Linear(hidden_size, hidden_size // 2),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(hidden_size // 2, output_size),
                    nn.Softmax(dim=-1)
                )
                
            def forward(self, x):
                # LSTM处理
                lstm_out, _ = self.lstm(x)
                
                # 注意力机制
                attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
                
                # 取最后时间步
                final_out = self.classifier(attn_out[:, -1, :])
                return final_out
        
        model = TradingLSTM(input_size, hidden_size, output_size).to(self.device)
        return model
    
    def predict(self, features):
        """预测交易信号"""
        if not TORCH_AVAILABLE or self.model is None:
            # 简单规则回退
            return {'action': 0, 'confidence': 0.5, 'probabilities': [0.33, 0.34, 0.33]}
        
        try:
            with torch.no_grad():
                if isinstance(features, np.ndarray):
                    features = torch.FloatTensor(features).to(self.device)
                
                if len(features.shape) == 2:
                    features = features.unsqueeze(0)  # 添加batch维度
                
                output = self.model(features)
                probabilities = output.cpu().numpy()[0]
                action = np.argmax(probabilities) - 1  # -1, 0, 1
                confidence = np.max(probabilities)
                
                return {
                    'action': action,
                    'confidence': confidence,
                    'probabilities': probabilities.tolist()
                }
                
        except Exception as e:
            print(f"[DL_MODEL] 预测失败: {e}")
            return {'action': 0, 'confidence': 0.0, 'probabilities': [0.33, 0.34, 0.33]}

class VNPYDeepLearningStrategy(CtaTemplate):
    """VNPY + Signal System 1 + V100 GPU 深度学习策略"""
    
    author = "VNPY + Signal System 1 + V100"
    
    # 策略参数
    sequence_length = 20        # 序列长度
    confidence_threshold = 0.6  # 置信度阈值
    position_size = 1          # 仓位大小
    stop_loss_pct = 0.02       # 止损百分比
    take_profit_pct = 0.04     # 止盈百分比
    
    # 策略变量
    current_position = 0
    entry_price = 0.0
    model_confidence = 0.0
    signal_count = 0
    
    parameters = ["sequence_length", "confidence_threshold", "position_size", 
                 "stop_loss_pct", "take_profit_pct"]
    variables = ["current_position", "entry_price", "model_confidence", "signal_count"]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 初始化组件
        if VNPY_AVAILABLE:
            self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
            self.am = ArrayManager(size=200)
        
        # 初始化深度学习模型
        self.dl_model = DeepLearningTradingModel()
        
        # 初始化信号系统
        self.signal_system = None
        if SIGNAL_SYSTEM_AVAILABLE:
            self.signal_system = GPUSignalWrapperFixed()
            if self.signal_system.initialize():
                self.write_log("Signal System 1 GPU包装器初始化成功")
            else:
                self.write_log("Signal System 1 初始化失败，使用深度学习模型")
                self.signal_system = None
        
        # 数据缓存
        self.price_history = deque(maxlen=self.sequence_length)
        self.volume_history = deque(maxlen=self.sequence_length)
        self.feature_history = deque(maxlen=self.sequence_length)
        
        # 性能统计
        self.performance_stats = {
            'total_signals': 0,
            'successful_trades': 0,
            'total_pnl': 0.0,
            'avg_inference_time': 0.0
        }
        
    def on_init(self):
        """策略初始化"""
        self.write_log("VNPY深度学习策略初始化")
        if VNPY_AVAILABLE:
            self.load_bar(30)
        
    def on_start(self):
        """策略启动"""
        self.write_log("VNPY深度学习策略启动")
        self.trading = True
        
    def on_stop(self):
        """策略停止"""
        self.write_log("VNPY深度学习策略停止")
        self.trading = False
        
        # 输出性能统计
        stats = self.performance_stats
        self.write_log(f"性能统计: 信号={stats['total_signals']}, "
                      f"成功交易={stats['successful_trades']}, "
                      f"总盈亏={stats['total_pnl']:.2f}")
        
    def on_tick(self, tick: TickData):
        """处理tick数据"""
        if VNPY_AVAILABLE:
            self.bg.update_tick(tick)
        
    def on_bar(self, bar: BarData):
        """处理bar数据"""
        if VNPY_AVAILABLE:
            self.bg.update_bar(bar)
        
    def on_1min_bar(self, bar: BarData):
        """处理1分钟K线"""
        if not VNPY_AVAILABLE:
            return
            
        self.am.update_bar(bar)
        if not self.am.inited:
            return
            
        # 更新数据历史
        self._update_data_history(bar)
        
        # 生成交易信号
        signal_result = self._generate_trading_signal(bar)
        
        # 执行交易逻辑
        if signal_result and signal_result['confidence'] > self.confidence_threshold:
            self._execute_trading_logic(signal_result, bar)
            
        # 检查止损止盈
        self._check_stop_conditions(bar)
        
        if VNPY_AVAILABLE:
            self.put_event()
    
    def _update_data_history(self, bar):
        """更新数据历史"""
        self.price_history.append(bar.close_price)
        self.volume_history.append(bar.volume)
        
        # 计算技术特征
        if len(self.price_history) >= 5:
            features = self._calculate_features()
            self.feature_history.append(features)
    
    def _calculate_features(self):
        """计算技术特征"""
        prices = np.array(list(self.price_history))
        volumes = np.array(list(self.volume_history))
        
        # 基础特征
        returns = np.diff(prices) / prices[:-1] if len(prices) > 1 else np.array([0])
        volatility = np.std(returns) if len(returns) > 0 else 0
        
        # 技术指标特征
        sma_5 = np.mean(prices[-5:]) if len(prices) >= 5 else prices[-1]
        sma_10 = np.mean(prices[-10:]) if len(prices) >= 10 else sma_5
        
        # 价格位置
        price_position = (prices[-1] - sma_10) / sma_10 if sma_10 > 0 else 0
        
        # 成交量特征
        volume_ma = np.mean(volumes[-5:]) if len(volumes) >= 5 else volumes[-1]
        volume_ratio = volumes[-1] / volume_ma if volume_ma > 0 else 1
        
        return np.array([
            returns[-1] if len(returns) > 0 else 0,  # 最新收益率
            volatility,                               # 波动率
            price_position,                          # 价格位置
            volume_ratio,                            # 成交量比率
            np.sin(len(self.price_history) * 0.1),  # 周期特征
        ])
    
    def _generate_trading_signal(self, bar):
        """生成交易信号"""
        start_time = time.time()
        
        try:
            # 方法1: 使用Signal System 1
            if self.signal_system:
                market_data = {
                    'close': bar.close_price,
                    'volume': bar.volume,
                    'high': bar.high_price,
                    'low': bar.low_price,
                    'open': bar.open_price
                }
                
                signal_result = self.signal_system.generate_signal(market_data)
                if 'signal' in signal_result:
                    return {
                        'action': signal_result['signal'],
                        'confidence': signal_result.get('confidence', 0.5),
                        'source': 'signal_system_1',
                        'inference_time': time.time() - start_time
                    }
            
            # 方法2: 使用深度学习模型
            if len(self.feature_history) >= self.sequence_length:
                features = np.array(list(self.feature_history))
                dl_result = self.dl_model.predict(features)
                
                return {
                    'action': dl_result['action'],
                    'confidence': dl_result['confidence'],
                    'source': 'deep_learning',
                    'inference_time': time.time() - start_time
                }
            
            return None
            
        except Exception as e:
            self.write_log(f"信号生成失败: {e}")
            return None
    
    def _execute_trading_logic(self, signal_result, bar):
        """执行交易逻辑"""
        action = signal_result['action']
        confidence = signal_result['confidence']
        
        self.model_confidence = confidence
        self.signal_count += 1
        self.performance_stats['total_signals'] += 1
        
        current_price = bar.close_price
        
        # 买入信号
        if action == 1 and self.pos <= 0:
            if self.pos < 0:
                self.cover(current_price, abs(self.pos))
            self.buy(current_price, self.position_size)
            self.entry_price = current_price
            self.write_log(f"买入信号执行: 价格={current_price}, 置信度={confidence:.3f}, "
                          f"来源={signal_result.get('source', 'unknown')}")
            
        # 卖出信号
        elif action == -1 and self.pos >= 0:
            if self.pos > 0:
                self.sell(current_price, self.pos)
            self.short(current_price, self.position_size)
            self.entry_price = current_price
            self.write_log(f"卖出信号执行: 价格={current_price}, 置信度={confidence:.3f}, "
                          f"来源={signal_result.get('source', 'unknown')}")
    
    def _check_stop_conditions(self, bar):
        """检查止损止盈条件"""
        if self.pos == 0 or self.entry_price == 0:
            return
            
        current_price = bar.close_price
        
        # 多头止损止盈
        if self.pos > 0:
            pnl_pct = (current_price - self.entry_price) / self.entry_price
            
            if pnl_pct <= -self.stop_loss_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止损: {pnl_pct:.2%}")
                
            elif pnl_pct >= self.take_profit_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止盈: {pnl_pct:.2%}")
                
        # 空头止损止盈
        elif self.pos < 0:
            pnl_pct = (self.entry_price - current_price) / self.entry_price
            
            if pnl_pct <= -self.stop_loss_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止损: {pnl_pct:.2%}")
                
            elif pnl_pct >= self.take_profit_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止盈: {pnl_pct:.2%}")

# 测试函数
def test_strategy():
    """测试策略功能"""
    print("=" * 60)
    print("VNPY深度学习策略测试")
    print("=" * 60)
    
    # 创建策略实例
    strategy = VNPYDeepLearningStrategy(
        cta_engine=None,
        strategy_name="test_dl_strategy",
        vt_symbol="rb2501.SHFE",
        setting={}
    )
    
    strategy.on_init()
    strategy.on_start()
    
    print("✅ 策略测试完成")

if __name__ == "__main__":
    test_strategy()
