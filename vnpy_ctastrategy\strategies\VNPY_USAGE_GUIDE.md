# VNPY Signal System策略使用指南


📋 在VNPY中使用Signal System策略的步骤:

1. 🚀 启动VeighNa Studio
   - 打开VeighNa Studio应用程序
   - 确保CTA策略模块已加载

2. 📊 添加策略
   - 进入"CTA策略"界面
   - 点击"添加策略"按钮
   - 在策略列表中找到以下策略:
     * SignalSystemStrategy (推荐 - 简化版)
     * SignalSystemDeepLearningStrategy (完整版)

3. ⚙️ 配置策略参数
   SignalSystemStrategy参数:
   - confidence_threshold: 0.6 (置信度阈值)
   - position_size: 1 (仓位大小)
   - stop_loss_pct: 0.02 (止损百分比)
   - take_profit_pct: 0.04 (止盈百分比)

4. 🎯 选择交易品种
   - 推荐品种: rb2501.SHFE (螺纹钢主力)
   - 其他期货品种也可以使用

5. 🔄 启动策略
   - 点击"初始化"按钮
   - 等待策略初始化完成
   - 点击"启动"按钮开始交易

6. 📈 监控策略
   - 查看策略日志了解运行状态
   - 监控持仓和盈亏情况
   - 观察信号生成和执行情况

⚠️ 注意事项:
- 首次使用建议在模拟环境中测试
- 确保有足够的资金和风险承受能力
- 定期检查策略表现并调整参数
- 如遇问题请查看策略日志
