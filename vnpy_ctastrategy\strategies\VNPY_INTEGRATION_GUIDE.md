# VNPY策略集成指南

## 🎯 策略文件状态

根据测试结果，以下策略文件已准备就绪：

### ✅ SignalSystemStrategy (推荐)
- **文件**: `signal_system_strategy.py`
- **特点**: 简化版本，兼容性好，依赖少
- **功能**: 基础深度学习 + Signal System集成
- **推荐用途**: 生产环境，稳定交易

### ✅ SignalSystemDeepLearningStrategy (高级)
- **文件**: `signal_system_deep_learning_strategy.py`
- **特点**: 完整版本，功能丰富，性能强大
- **功能**: 高级深度学习 + V100 GPU优化
- **推荐用途**: 研究环境，高性能需求

## 🚀 在VNPY中使用步骤

### 方法1: 直接在VeighNa Studio中使用

1. **启动VeighNa Studio**
   ```
   # 如果已安装VeighNa Studio
   # 直接启动应用程序
   ```

2. **进入CTA策略模块**
   - 点击"CTA策略"选项卡
   - 策略应该自动出现在策略列表中

3. **添加策略**
   - 点击"添加策略"
   - 选择 `SignalSystemStrategy` 或 `SignalSystemDeepLearningStrategy`
   - 配置参数并启动

### 方法2: 编程方式集成

```python
# 导入策略类
from vnpy_ctastrategy.strategies.signal_system_strategy import SignalSystemStrategy

# 创建策略实例
strategy = SignalSystemStrategy(
    cta_engine=cta_engine,
    strategy_name="my_signal_strategy",
    vt_symbol="rb2501.SHFE",
    setting={
        "confidence_threshold": 0.6,
        "position_size": 1,
        "stop_loss_pct": 0.02,
        "take_profit_pct": 0.04
    }
)

# 初始化和启动
strategy.on_init()
strategy.on_start()
```

## ⚙️ 策略参数配置

### SignalSystemStrategy参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| confidence_threshold | 0.6 | 信号置信度阈值 |
| position_size | 1 | 交易仓位大小 |
| stop_loss_pct | 0.02 | 止损百分比 |
| take_profit_pct | 0.04 | 止盈百分比 |

### 推荐配置

**保守型**:
```python
{
    "confidence_threshold": 0.7,  # 高置信度
    "position_size": 1,
    "stop_loss_pct": 0.015,       # 较小止损
    "take_profit_pct": 0.03       # 较小止盈
}
```

**激进型**:
```python
{
    "confidence_threshold": 0.5,  # 较低置信度
    "position_size": 2,           # 较大仓位
    "stop_loss_pct": 0.025,       # 较大止损
    "take_profit_pct": 0.05       # 较大止盈
}
```

## 🎯 适用品种

推荐交易品种：
- **螺纹钢**: rb2501.SHFE (主力合约)
- **铁矿石**: i2501.DCE
- **焦炭**: j2501.DCE
- **铜**: cu2501.SHFE
- **黄金**: au2501.SHFE

## 📊 监控指标

关注以下策略表现指标：
- **信号数量**: 每日生成信号次数
- **信号准确率**: 盈利信号占比
- **平均持仓时间**: 单次交易持续时间
- **最大回撤**: 策略最大亏损幅度
- **夏普比率**: 风险调整后收益

## ⚠️ 注意事项

1. **首次使用**: 建议在模拟环境中测试
2. **资金管理**: 控制单次交易风险
3. **参数调优**: 根据不同品种调整参数
4. **定期检查**: 监控策略表现并及时调整
5. **风险控制**: 严格执行止损止盈规则

## 🔧 故障排除

### 常见问题

**Q: 策略在VNPY中看不到**
A: 检查__init__.py文件是否正确注册策略

**Q: 策略启动失败**
A: 检查依赖包是否安装完整 (numpy, torch等)

**Q: GPU不可用**
A: 策略会自动回退到CPU模式，不影响使用

**Q: 信号生成异常**
A: 查看策略日志，检查数据输入是否正常

### 技术支持

如遇问题，请检查：
1. 策略日志输出
2. Python环境依赖
3. 数据连接状态
4. 参数配置正确性

---

**策略已准备就绪，祝你交易成功！** 🚀
