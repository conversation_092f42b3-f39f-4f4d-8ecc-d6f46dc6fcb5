#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNPY + Signal System + V100 GPU 深度学习策略
结合signal_system_1信号生成器和深度学习模型的完整交易策略

特性:
- 集成Signal System 1 C++信号生成器
- V100 GPU加速深度学习推理
- 多时间框架技术分析
- 智能风险管理
- 自适应学习机制
"""

import os
import sys
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import deque
import traceback

# 添加signal_system路径
current_dir = os.path.dirname(os.path.abspath(__file__))
signal_system_path = os.path.join(current_dir, 'signal_system')
sys.path.insert(0, signal_system_path)

# VNPY核心导入
try:
    from vnpy_ctastrategy import (
        CtaTemplate,
        StopOrder,
        TickData,
        BarData,
        TradeData,
        OrderData,
        BarGenerator,
        ArrayManager,
    )
    from vnpy.trader.constant import Direction, Offset, Status
    VNPY_AVAILABLE = True
    print("[INFO] VNPY模块导入成功")
except ImportError as e:
    print(f"[WARNING] VNPY模块导入失败: {e}")
    VNPY_AVAILABLE = False
    
    # 模拟VNPY类用于测试
    class CtaTemplate:
        def __init__(self, cta_engine=None, strategy_name="", vt_symbol="", setting=None):
            self.pos = 0
            self.trading = False
            self.cta_engine = cta_engine
            self.strategy_name = strategy_name
            self.vt_symbol = vt_symbol
            
        def write_log(self, msg): print(f"[{self.strategy_name}] {msg}")
        def buy(self, price, volume, stop=False): self.pos += volume
        def sell(self, price, volume, stop=False): self.pos -= volume
        def cover(self, price, volume, stop=False): self.pos += volume
        def short(self, price, volume, stop=False): self.pos -= volume
        def cancel_all(self): pass
        def put_event(self): pass
        def load_bar(self, days): pass

# 深度学习框架导入
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
    print(f"[INFO] PyTorch {torch.__version__} 可用")
    
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name(0)
        print(f"[INFO] GPU可用: {device_name}")
        if "V100" in device_name:
            print("[INFO] 检测到Tesla V100，启用专用优化")
    else:
        print("[WARNING] CUDA不可用，使用CPU模式")
        
except ImportError:
    print("[WARNING] PyTorch不可用，使用简化模式")
    TORCH_AVAILABLE = False

# Signal System导入
try:
    from gpu_wrapper import GPUSignalWrapperFixed
    SIGNAL_SYSTEM_AVAILABLE = True
    print("[INFO] Signal System GPU包装器可用")
except ImportError:
    print("[WARNING] Signal System不可用，使用模拟信号")
    SIGNAL_SYSTEM_AVAILABLE = False

class DeepLearningModel:
    """深度学习交易模型"""
    
    def __init__(self, input_size=30, hidden_size=128, output_size=3, sequence_length=20):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        self.sequence_length = sequence_length
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        
        if TORCH_AVAILABLE:
            self.model = self._create_model()
            print(f"[DL_MODEL] 模型已创建，设备: {self.device}")
        
    def _create_model(self):
        """创建深度学习模型"""
        class TradingTransformer(nn.Module):
            def __init__(self, input_size, hidden_size, output_size, num_heads=8, num_layers=3):
                super(TradingTransformer, self).__init__()
                self.input_size = input_size
                self.hidden_size = hidden_size
                
                # 输入投影层
                self.input_projection = nn.Linear(input_size, hidden_size)
                
                # 位置编码
                self.positional_encoding = nn.Parameter(torch.randn(1000, hidden_size))
                
                # Transformer编码器
                encoder_layer = nn.TransformerEncoderLayer(
                    d_model=hidden_size,
                    nhead=num_heads,
                    dim_feedforward=hidden_size * 4,
                    dropout=0.1,
                    batch_first=True
                )
                self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
                
                # 输出层
                self.output_projection = nn.Sequential(
                    nn.Linear(hidden_size, hidden_size // 2),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(hidden_size // 2, output_size),
                    nn.Softmax(dim=-1)
                )
                
            def forward(self, x):
                batch_size, seq_len, _ = x.shape
                
                # 输入投影
                x = self.input_projection(x)
                
                # 添加位置编码
                pos_encoding = self.positional_encoding[:seq_len, :].unsqueeze(0).expand(batch_size, -1, -1)
                x = x + pos_encoding
                
                # Transformer编码
                x = self.transformer(x)
                
                # 取最后时间步的输出
                x = x[:, -1, :]
                
                # 输出投影
                output = self.output_projection(x)
                return output
        
        model = TradingTransformer(
            self.input_size, 
            self.hidden_size, 
            self.output_size
        ).to(self.device)
        
        return model
    
    def predict(self, features):
        """预测交易信号"""
        if not TORCH_AVAILABLE or self.model is None:
            # 简单规则回退
            return {'action': 0, 'confidence': 0.5, 'probabilities': [0.33, 0.34, 0.33]}
        
        try:
            with torch.no_grad():
                if isinstance(features, np.ndarray):
                    features = torch.FloatTensor(features).to(self.device)
                
                if len(features.shape) == 2:
                    features = features.unsqueeze(0)  # 添加batch维度
                
                output = self.model(features)
                probabilities = output.cpu().numpy()[0]
                action = np.argmax(probabilities) - 1  # -1, 0, 1
                confidence = np.max(probabilities)
                
                return {
                    'action': action,
                    'confidence': confidence,
                    'probabilities': probabilities.tolist()
                }
                
        except Exception as e:
            print(f"[DL_MODEL] 预测失败: {e}")
            return {'action': 0, 'confidence': 0.0, 'probabilities': [0.33, 0.34, 0.33]}

class TechnicalAnalyzer:
    """技术分析器"""
    
    def __init__(self, max_length=200):
        self.max_length = max_length
        self.price_data = deque(maxlen=max_length)
        self.volume_data = deque(maxlen=max_length)
        
    def update(self, price, volume):
        """更新数据"""
        self.price_data.append(price)
        self.volume_data.append(volume)
    
    def calculate_features(self, sequence_length=20):
        """计算技术特征"""
        if len(self.price_data) < sequence_length:
            return None
            
        prices = np.array(list(self.price_data)[-sequence_length:])
        volumes = np.array(list(self.volume_data)[-sequence_length:])
        
        features = []
        
        for i in range(sequence_length):
            # 基础价格特征
            price = prices[i]
            volume = volumes[i]
            
            # 收益率特征
            if i > 0:
                return_1 = (prices[i] - prices[i-1]) / prices[i-1]
            else:
                return_1 = 0
                
            if i > 4:
                return_5 = (prices[i] - prices[i-5]) / prices[i-5]
            else:
                return_5 = 0
            
            # 移动平均特征
            if i >= 4:
                ma_5 = np.mean(prices[max(0, i-4):i+1])
                price_ma_ratio = price / ma_5 - 1
            else:
                price_ma_ratio = 0
                
            if i >= 9:
                ma_10 = np.mean(prices[max(0, i-9):i+1])
                ma_ratio = ma_5 / ma_10 - 1 if i >= 4 else 0
            else:
                ma_ratio = 0
            
            # 波动率特征
            if i >= 4:
                volatility = np.std(prices[max(0, i-4):i+1])
            else:
                volatility = 0
            
            # 成交量特征
            if i >= 4:
                volume_ma = np.mean(volumes[max(0, i-4):i+1])
                volume_ratio = volume / volume_ma if volume_ma > 0 else 1
            else:
                volume_ratio = 1
            
            # 组合特征向量
            feature_vector = [
                return_1,           # 1日收益率
                return_5,           # 5日收益率
                price_ma_ratio,     # 价格相对MA5偏离
                ma_ratio,           # MA5相对MA10偏离
                volatility,         # 波动率
                volume_ratio,       # 成交量比率
            ]
            
            features.append(feature_vector)
        
        return np.array(features)

class SignalSystemDeepLearningStrategy(CtaTemplate):
    """Signal System + 深度学习策略"""
    
    author = "VNPY + Signal System + V100"
    
    # 策略参数
    sequence_length = 20        # 时序长度
    confidence_threshold = 0.65 # 置信度阈值
    position_size = 1          # 仓位大小
    stop_loss_pct = 0.015      # 止损百分比
    take_profit_pct = 0.03     # 止盈百分比
    signal_weight_dl = 0.6     # 深度学习信号权重
    signal_weight_sys = 0.4    # Signal System权重
    
    # 策略变量
    current_position = 0
    entry_price = 0.0
    model_confidence = 0.0
    signal_count = 0
    last_signal_time = 0
    
    parameters = [
        "sequence_length", "confidence_threshold", "position_size", 
        "stop_loss_pct", "take_profit_pct", "signal_weight_dl", "signal_weight_sys"
    ]
    variables = [
        "current_position", "entry_price", "model_confidence", "signal_count"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 初始化VNPY组件
        if VNPY_AVAILABLE:
            self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
            self.am = ArrayManager(size=300)
        
        # 初始化深度学习模型
        self.dl_model = DeepLearningModel(
            input_size=6,
            sequence_length=self.sequence_length
        )
        
        # 初始化技术分析器
        self.technical_analyzer = TechnicalAnalyzer()
        
        # 初始化Signal System
        self.signal_system = None
        if SIGNAL_SYSTEM_AVAILABLE:
            try:
                self.signal_system = GPUSignalWrapperFixed()
                if self.signal_system.initialize():
                    self.write_log("Signal System GPU包装器初始化成功")
                else:
                    self.write_log("Signal System初始化失败")
                    self.signal_system = None
            except Exception as e:
                self.write_log(f"Signal System初始化异常: {e}")
                self.signal_system = None
        
        # 性能统计
        self.performance_stats = {
            'total_signals': 0,
            'dl_signals': 0,
            'sys_signals': 0,
            'successful_trades': 0,
            'total_pnl': 0.0,
            'avg_inference_time': 0.0
        }
        
        self.write_log("策略初始化完成")
        
    def on_init(self):
        """策略初始化"""
        self.write_log("Signal System深度学习策略初始化")
        if VNPY_AVAILABLE:
            self.load_bar(50)
        
    def on_start(self):
        """策略启动"""
        self.write_log("Signal System深度学习策略启动")
        self.trading = True
        
    def on_stop(self):
        """策略停止"""
        self.write_log("Signal System深度学习策略停止")
        self.trading = False
        
        # 输出性能统计
        stats = self.performance_stats
        self.write_log(f"性能统计:")
        self.write_log(f"  总信号数: {stats['total_signals']}")
        self.write_log(f"  深度学习信号: {stats['dl_signals']}")
        self.write_log(f"  Signal System信号: {stats['sys_signals']}")
        self.write_log(f"  成功交易: {stats['successful_trades']}")
        self.write_log(f"  总盈亏: {stats['total_pnl']:.2f}")
        
    def on_tick(self, tick):
        """处理tick数据"""
        if VNPY_AVAILABLE:
            self.bg.update_tick(tick)

    def on_bar(self, bar):
        """处理bar数据"""
        if VNPY_AVAILABLE:
            self.bg.update_bar(bar)

    def on_1min_bar(self, bar):
        """处理1分钟K线"""
        if not VNPY_AVAILABLE:
            return
            
        self.am.update_bar(bar)
        if not self.am.inited:
            return
            
        # 更新技术分析器
        self.technical_analyzer.update(bar.close_price, bar.volume)
        
        # 生成交易信号
        signal_result = self._generate_combined_signal(bar)
        
        # 执行交易逻辑
        if signal_result and signal_result['confidence'] > self.confidence_threshold:
            self._execute_trading_logic(signal_result, bar)
            
        # 检查止损止盈
        self._check_stop_conditions(bar)
        
        if VNPY_AVAILABLE:
            self.put_event()
    
    def _generate_combined_signal(self, bar):
        """生成组合信号"""
        start_time = time.time()
        
        try:
            dl_signal = None
            sys_signal = None
            
            # 1. 深度学习信号
            features = self.technical_analyzer.calculate_features(self.sequence_length)
            if features is not None:
                dl_result = self.dl_model.predict(features)
                dl_signal = {
                    'action': dl_result['action'],
                    'confidence': dl_result['confidence'],
                    'source': 'deep_learning'
                }
                self.performance_stats['dl_signals'] += 1
            
            # 2. Signal System信号
            if self.signal_system:
                market_data = {
                    'close': bar.close_price,
                    'volume': bar.volume,
                    'high': bar.high_price,
                    'low': bar.low_price,
                    'open': bar.open_price
                }
                
                sys_result = self.signal_system.generate_signal(market_data)
                if 'signal' in sys_result:
                    sys_signal = {
                        'action': sys_result['signal'],
                        'confidence': sys_result.get('confidence', 0.5),
                        'source': 'signal_system'
                    }
                    self.performance_stats['sys_signals'] += 1
            
            # 3. 信号融合
            if dl_signal and sys_signal:
                # 加权融合
                combined_action = (
                    dl_signal['action'] * self.signal_weight_dl + 
                    sys_signal['action'] * self.signal_weight_sys
                )
                combined_confidence = (
                    dl_signal['confidence'] * self.signal_weight_dl + 
                    sys_signal['confidence'] * self.signal_weight_sys
                )
                
                # 决定最终动作
                if abs(combined_action) > 0.3:
                    final_action = 1 if combined_action > 0 else -1
                else:
                    final_action = 0
                
                return {
                    'action': final_action,
                    'confidence': combined_confidence,
                    'source': 'combined',
                    'dl_action': dl_signal['action'],
                    'sys_action': sys_signal['action'],
                    'inference_time': time.time() - start_time
                }
            
            elif dl_signal:
                dl_signal['inference_time'] = time.time() - start_time
                return dl_signal
            elif sys_signal:
                sys_signal['inference_time'] = time.time() - start_time
                return sys_signal
            
            return None
            
        except Exception as e:
            self.write_log(f"信号生成失败: {e}")
            return None

    def _execute_trading_logic(self, signal_result, bar):
        """执行交易逻辑"""
        action = signal_result['action']
        confidence = signal_result['confidence']
        source = signal_result.get('source', 'unknown')

        self.model_confidence = confidence
        self.signal_count += 1
        self.performance_stats['total_signals'] += 1

        current_price = bar.close_price
        current_time = time.time()

        # 防止频繁交易 - 至少间隔60秒
        if current_time - self.last_signal_time < 60:
            return

        # 买入信号
        if action == 1 and self.pos <= 0:
            if self.pos < 0:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"平空仓: 价格={current_price}")

            self.buy(current_price, self.position_size)
            self.entry_price = current_price
            self.last_signal_time = current_time

            log_msg = f"买入信号执行: 价格={current_price}, 置信度={confidence:.3f}, 来源={source}"
            if 'dl_action' in signal_result:
                log_msg += f", DL={signal_result['dl_action']}, SYS={signal_result['sys_action']}"
            self.write_log(log_msg)

        # 卖出信号
        elif action == -1 and self.pos >= 0:
            if self.pos > 0:
                self.sell(current_price, self.pos)
                self.write_log(f"平多仓: 价格={current_price}")

            self.short(current_price, self.position_size)
            self.entry_price = current_price
            self.last_signal_time = current_time

            log_msg = f"卖出信号执行: 价格={current_price}, 置信度={confidence:.3f}, 来源={source}"
            if 'dl_action' in signal_result:
                log_msg += f", DL={signal_result['dl_action']}, SYS={signal_result['sys_action']}"
            self.write_log(log_msg)

    def _check_stop_conditions(self, bar):
        """检查止损止盈条件"""
        if self.pos == 0 or self.entry_price == 0:
            return

        current_price = bar.close_price

        # 多头止损止盈
        if self.pos > 0:
            pnl_pct = (current_price - self.entry_price) / self.entry_price

            if pnl_pct <= -self.stop_loss_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止损: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0

            elif pnl_pct >= self.take_profit_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止盈: {pnl_pct:.2%}, 价格={current_price}")
                self.performance_stats['successful_trades'] += 1
                self.entry_price = 0

        # 空头止损止盈
        elif self.pos < 0:
            pnl_pct = (self.entry_price - current_price) / self.entry_price

            if pnl_pct <= -self.stop_loss_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止损: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0

            elif pnl_pct >= self.take_profit_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止盈: {pnl_pct:.2%}, 价格={current_price}")
                self.performance_stats['successful_trades'] += 1
                self.entry_price = 0

    def on_trade(self, trade):
        """处理成交回报"""
        if VNPY_AVAILABLE:
            self.current_position = self.pos
            self.put_event()

    def on_order(self, order):
        """处理委托回报"""
        pass

    def on_stop_order(self, stop_order):
        """处理停止单回报"""
        pass

# 测试函数
def test_strategy():
    """测试策略功能"""
    print("=" * 80)
    print("Signal System深度学习策略测试")
    print("=" * 80)

    # 创建策略实例
    strategy = SignalSystemDeepLearningStrategy(
        cta_engine=None,
        strategy_name="test_signal_dl_strategy",
        vt_symbol="rb2501.SHFE",
        setting={
            "confidence_threshold": 0.65,
            "position_size": 1,
            "sequence_length": 20
        }
    )

    strategy.on_init()
    strategy.on_start()

    print("✅ 策略测试完成")
    print(f"深度学习模型: {'✅' if strategy.dl_model.model is not None else '❌'}")
    print(f"Signal System: {'✅' if strategy.signal_system is not None else '❌'}")
    print(f"技术分析器: ✅")

if __name__ == "__main__":
    test_strategy()
