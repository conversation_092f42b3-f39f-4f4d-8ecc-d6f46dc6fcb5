#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
独立Signal System策略
不依赖外部包，直接可用于VNPY
完全兼容任何VNPY环境
"""

from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
from vnpy.trader.constant import Direction, Offset, Status

class IndependentSignalStrategy(CtaTemplate):
    """独立Signal System策略"""
    
    author = "VNPY + Signal System"
    
    # 策略参数
    confidence_threshold = 0.6   # 置信度阈值
    position_size = 1           # 仓位大小
    stop_loss_pct = 0.02        # 止损百分比
    take_profit_pct = 0.04      # 止盈百分比
    ma_period = 20              # 移动平均周期
    volume_period = 10          # 成交量均线周期
    
    # 策略变量
    current_position = 0
    entry_price = 0.0
    signal_confidence = 0.0
    signal_count = 0
    ma_value = 0.0
    last_price = 0.0
    
    parameters = [
        "confidence_threshold", 
        "position_size", 
        "stop_loss_pct", 
        "take_profit_pct",
        "ma_period",
        "volume_period"
    ]
    variables = [
        "current_position", 
        "entry_price", 
        "signal_confidence", 
        "signal_count",
        "ma_value",
        "last_price"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 初始化VNPY组件
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=100)
        
        # 数据缓存
        self.price_history = []
        self.volume_history = []
        self.last_signal_time = 0
        
        self.write_log("独立Signal System策略初始化完成")
        
    def on_init(self):
        """策略初始化"""
        self.write_log("独立Signal System策略初始化")
        self.load_bar(50)  # 加载50根历史K线
        
    def on_start(self):
        """策略启动"""
        self.write_log("独立Signal System策略启动")
        self.trading = True
        
    def on_stop(self):
        """策略停止"""
        self.write_log("独立Signal System策略停止")
        self.trading = False
        
        # 输出策略统计
        self.write_log(f"策略统计: 总信号={self.signal_count}, 当前仓位={self.pos}")
        
    def on_tick(self, tick: TickData):
        """处理tick数据"""
        self.bg.update_tick(tick)
        
    def on_bar(self, bar: BarData):
        """处理bar数据"""
        self.bg.update_bar(bar)
        
    def on_1min_bar(self, bar: BarData):
        """处理1分钟K线"""
        self.am.update_bar(bar)
        if not self.am.inited:
            return
            
        # 更新数据历史
        self.price_history.append(bar.close_price)
        self.volume_history.append(bar.volume)
        self.last_price = bar.close_price
        
        # 保持历史数据长度
        if len(self.price_history) > 100:
            self.price_history.pop(0)
        if len(self.volume_history) > 100:
            self.volume_history.pop(0)
        
        # 计算技术指标
        if len(self.am.close_array) >= self.ma_period:
            self.ma_value = self.am.sma(self.ma_period)
        
        # 生成交易信号
        signal_result = self._generate_signal(bar)
        
        # 执行交易逻辑
        if signal_result and signal_result['confidence'] > self.confidence_threshold:
            self._execute_trading_logic(signal_result, bar)
            
        # 检查止损止盈
        self._check_stop_conditions(bar)
        
        # 更新界面
        self.put_event()
    
    def _generate_signal(self, bar):
        """生成交易信号"""
        try:
            # 需要足够的历史数据
            if len(self.price_history) < 10 or self.ma_value == 0:
                return None
            
            current_price = bar.close_price
            
            # 1. 价格相对移动平均线的位置
            price_ma_ratio = (current_price - self.ma_value) / self.ma_value
            
            # 2. 价格动量 (最近5根K线的变化)
            if len(self.price_history) >= 5:
                recent_prices = self.price_history[-5:]
                price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            else:
                price_change = 0
            
            # 3. 成交量确认
            if len(self.volume_history) >= self.volume_period:
                recent_volumes = self.volume_history[-self.volume_period:]
                avg_volume = sum(recent_volumes) / len(recent_volumes)
                volume_ratio = bar.volume / avg_volume if avg_volume > 0 else 1
            else:
                volume_ratio = 1
            
            # 4. 波动率计算
            if len(self.price_history) >= 10:
                recent_prices = self.price_history[-10:]
                price_changes = []
                for i in range(1, len(recent_prices)):
                    change = (recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
                    price_changes.append(change)
                
                # 简单标准差计算
                if price_changes:
                    avg_change = sum(price_changes) / len(price_changes)
                    variance = sum((x - avg_change) ** 2 for x in price_changes) / len(price_changes)
                    volatility = variance ** 0.5
                else:
                    volatility = 0
            else:
                volatility = 0
            
            # 5. RSI简化计算
            if len(self.price_history) >= 14:
                gains = []
                losses = []
                for i in range(1, min(15, len(self.price_history))):
                    change = self.price_history[-i] - self.price_history[-i-1]
                    if change > 0:
                        gains.append(change)
                        losses.append(0)
                    else:
                        gains.append(0)
                        losses.append(-change)
                
                avg_gain = sum(gains) / len(gains) if gains else 0
                avg_loss = sum(losses) / len(losses) if losses else 0
                
                if avg_loss > 0:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                else:
                    rsi = 100
            else:
                rsi = 50
            
            # 信号生成逻辑
            signal = 0
            confidence = 0.5
            
            # 买入条件：
            # - 价格突破MA向上
            # - 有正向动量
            # - 成交量放大
            # - RSI不过度超买
            if (price_ma_ratio > 0.005 and 
                price_change > 0.01 and 
                volume_ratio > 1.2 and 
                rsi < 70 and
                volatility > 0.005):
                
                signal = 1
                # 置信度基于多个因子
                confidence = min(0.85, 0.5 + 
                               abs(price_ma_ratio) * 20 + 
                               abs(price_change) * 10 + 
                               (volume_ratio - 1) * 0.1 +
                               volatility * 5)
                
            # 卖出条件：
            # - 价格跌破MA向下
            # - 有负向动量  
            # - 成交量放大
            # - RSI不过度超卖
            elif (price_ma_ratio < -0.005 and 
                  price_change < -0.01 and 
                  volume_ratio > 1.2 and 
                  rsi > 30 and
                  volatility > 0.005):
                
                signal = -1
                confidence = min(0.85, 0.5 + 
                               abs(price_ma_ratio) * 20 + 
                               abs(price_change) * 10 + 
                               (volume_ratio - 1) * 0.1 +
                               volatility * 5)
            
            return {
                'action': signal,
                'confidence': confidence,
                'price_ma_ratio': price_ma_ratio,
                'price_change': price_change,
                'volume_ratio': volume_ratio,
                'rsi': rsi,
                'volatility': volatility
            }
            
        except Exception as e:
            self.write_log(f"信号生成失败: {e}")
            return None
    
    def _execute_trading_logic(self, signal_result, bar):
        """执行交易逻辑"""
        action = signal_result['action']
        confidence = signal_result['confidence']
        
        self.signal_confidence = confidence
        self.signal_count += 1
        
        current_price = bar.close_price
        import time
        current_time = time.time()
        
        # 防止频繁交易 - 至少间隔60秒
        if current_time - self.last_signal_time < 60:
            return
        
        # 买入信号
        if action == 1 and self.pos <= 0:
            if self.pos < 0:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"平空仓: 价格={current_price}")
            
            self.buy(current_price, self.position_size)
            self.entry_price = current_price
            self.last_signal_time = current_time
            
            self.write_log(f"买入信号执行: 价格={current_price}, 置信度={confidence:.3f}")
            self.write_log(f"  技术指标: MA比率={signal_result['price_ma_ratio']:.4f}, "
                          f"动量={signal_result['price_change']:.4f}, "
                          f"RSI={signal_result['rsi']:.1f}")
            
        # 卖出信号
        elif action == -1 and self.pos >= 0:
            if self.pos > 0:
                self.sell(current_price, self.pos)
                self.write_log(f"平多仓: 价格={current_price}")
            
            self.short(current_price, self.position_size)
            self.entry_price = current_price
            self.last_signal_time = current_time
            
            self.write_log(f"卖出信号执行: 价格={current_price}, 置信度={confidence:.3f}")
            self.write_log(f"  技术指标: MA比率={signal_result['price_ma_ratio']:.4f}, "
                          f"动量={signal_result['price_change']:.4f}, "
                          f"RSI={signal_result['rsi']:.1f}")
    
    def _check_stop_conditions(self, bar):
        """检查止损止盈条件"""
        if self.pos == 0 or self.entry_price == 0:
            return
            
        current_price = bar.close_price
        
        # 多头止损止盈
        if self.pos > 0:
            pnl_pct = (current_price - self.entry_price) / self.entry_price
            
            if pnl_pct <= -self.stop_loss_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止损: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0
                
            elif pnl_pct >= self.take_profit_pct:
                self.sell(current_price, self.pos)
                self.write_log(f"多头止盈: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0
                
        # 空头止损止盈
        elif self.pos < 0:
            pnl_pct = (self.entry_price - current_price) / self.entry_price
            
            if pnl_pct <= -self.stop_loss_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止损: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0
                
            elif pnl_pct >= self.take_profit_pct:
                self.cover(current_price, abs(self.pos))
                self.write_log(f"空头止盈: {pnl_pct:.2%}, 价格={current_price}")
                self.entry_price = 0
    
    def on_trade(self, trade: TradeData):
        """处理成交回报"""
        self.current_position = self.pos
        self.put_event()
    
    def on_order(self, order: OrderData):
        """处理委托回报"""
        pass
    
    def on_stop_order(self, stop_order: StopOrder):
        """处理停止单回报"""
        pass
