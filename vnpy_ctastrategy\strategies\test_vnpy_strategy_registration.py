#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试VNPY策略注册
验证策略是否能被VNPY正确识别和加载
"""

import os
import sys
import importlib
from pathlib import Path

def test_strategy_import():
    """测试策略导入"""
    print("=" * 60)
    print("VNPY策略注册测试")
    print("=" * 60)
    
    # 测试策略模块导入
    try:
        import vnpy_ctastrategy.strategies as strategies_module
        print("✅ 策略模块导入成功")
        
        # 检查__all__列表
        if hasattr(strategies_module, '__all__'):
            all_strategies = strategies_module.__all__
            print(f"✅ 发现 {len(all_strategies)} 个注册策略:")
            for i, strategy_name in enumerate(all_strategies, 1):
                print(f"  {i:2d}. {strategy_name}")
        else:
            print("❌ 策略模块没有__all__属性")
            return False
            
    except ImportError as e:
        print(f"❌ 策略模块导入失败: {e}")
        return False
    
    return True

def test_specific_strategy_import():
    """测试具体策略导入"""
    print("\n" + "=" * 60)
    print("具体策略导入测试")
    print("=" * 60)
    
    strategies_to_test = [
        ("SignalSystemStrategy", "signal_system_strategy"),
        ("SignalSystemDeepLearningStrategy", "signal_system_deep_learning_strategy")
    ]
    
    success_count = 0
    
    for strategy_class, strategy_module in strategies_to_test:
        try:
            # 尝试导入策略类
            module_path = f"vnpy_ctastrategy.strategies.{strategy_module}"
            module = importlib.import_module(module_path)
            strategy_cls = getattr(module, strategy_class)
            
            print(f"✅ {strategy_class}")
            print(f"   模块: {module_path}")
            print(f"   作者: {getattr(strategy_cls, 'author', 'Unknown')}")
            print(f"   参数: {getattr(strategy_cls, 'parameters', [])}")
            print(f"   变量: {getattr(strategy_cls, 'variables', [])}")
            
            success_count += 1
            
        except ImportError as e:
            print(f"❌ {strategy_class}: 导入失败 - {e}")
        except AttributeError as e:
            print(f"❌ {strategy_class}: 类不存在 - {e}")
        except Exception as e:
            print(f"❌ {strategy_class}: 其他错误 - {e}")
    
    print(f"\n策略导入成功率: {success_count}/{len(strategies_to_test)} ({success_count/len(strategies_to_test)*100:.1f}%)")
    return success_count == len(strategies_to_test)

def test_strategy_instantiation():
    """测试策略实例化"""
    print("\n" + "=" * 60)
    print("策略实例化测试")
    print("=" * 60)
    
    try:
        from vnpy_ctastrategy.strategies.signal_system_strategy import SignalSystemStrategy
        
        # 模拟CTA引擎
        class MockCtaEngine:
            def write_log(self, msg):
                print(f"    [策略日志] {msg}")
        
        # 创建策略实例
        strategy = SignalSystemStrategy(
            cta_engine=MockCtaEngine(),
            strategy_name="test_signal_strategy",
            vt_symbol="rb2501.SHFE",
            setting={
                "confidence_threshold": 0.6,
                "position_size": 1
            }
        )
        
        print("✅ 策略实例化成功")
        print(f"   策略名称: {strategy.strategy_name}")
        print(f"   交易品种: {strategy.vt_symbol}")
        print(f"   置信度阈值: {strategy.confidence_threshold}")
        print(f"   仓位大小: {strategy.position_size}")
        
        # 测试策略初始化
        strategy.on_init()
        print("✅ 策略初始化成功")
        
        # 测试策略启动
        strategy.on_start()
        print("✅ 策略启动成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略实例化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vnpy_discovery():
    """测试VNPY策略发现机制"""
    print("\n" + "=" * 60)
    print("VNPY策略发现机制测试")
    print("=" * 60)
    
    try:
        # 模拟VNPY策略发现过程
        import vnpy_ctastrategy.strategies as strategies_module
        
        discovered_strategies = []
        
        if hasattr(strategies_module, '__all__'):
            for strategy_name in strategies_module.__all__:
                try:
                    # 尝试获取策略类
                    strategy_cls = getattr(strategies_module, strategy_name, None)
                    if strategy_cls is not None:
                        discovered_strategies.append({
                            'name': strategy_name,
                            'class': strategy_cls,
                            'author': getattr(strategy_cls, 'author', 'Unknown'),
                            'parameters': getattr(strategy_cls, 'parameters', [])
                        })
                except Exception as e:
                    print(f"⚠️  策略 {strategy_name} 发现失败: {e}")
        
        print(f"✅ VNPY可发现策略数量: {len(discovered_strategies)}")
        
        for strategy_info in discovered_strategies:
            print(f"   📋 {strategy_info['name']}")
            print(f"      作者: {strategy_info['author']}")
            print(f"      参数数量: {len(strategy_info['parameters'])}")
        
        # 检查我们的策略是否在列表中
        our_strategies = ['SignalSystemStrategy', 'SignalSystemDeepLearningStrategy']
        found_our_strategies = [s['name'] for s in discovered_strategies if s['name'] in our_strategies]
        
        print(f"\n我们的策略发现情况:")
        for strategy_name in our_strategies:
            if strategy_name in found_our_strategies:
                print(f"   ✅ {strategy_name} - 已发现")
            else:
                print(f"   ❌ {strategy_name} - 未发现")
        
        return len(found_our_strategies) > 0
        
    except Exception as e:
        print(f"❌ VNPY策略发现测试失败: {e}")
        return False

def create_vnpy_usage_guide():
    """创建VNPY使用指南"""
    print("\n" + "=" * 60)
    print("VNPY使用指南")
    print("=" * 60)
    
    guide = """
📋 在VNPY中使用Signal System策略的步骤:

1. 🚀 启动VeighNa Studio
   - 打开VeighNa Studio应用程序
   - 确保CTA策略模块已加载

2. 📊 添加策略
   - 进入"CTA策略"界面
   - 点击"添加策略"按钮
   - 在策略列表中找到以下策略:
     * SignalSystemStrategy (推荐 - 简化版)
     * SignalSystemDeepLearningStrategy (完整版)

3. ⚙️ 配置策略参数
   SignalSystemStrategy参数:
   - confidence_threshold: 0.6 (置信度阈值)
   - position_size: 1 (仓位大小)
   - stop_loss_pct: 0.02 (止损百分比)
   - take_profit_pct: 0.04 (止盈百分比)

4. 🎯 选择交易品种
   - 推荐品种: rb2501.SHFE (螺纹钢主力)
   - 其他期货品种也可以使用

5. 🔄 启动策略
   - 点击"初始化"按钮
   - 等待策略初始化完成
   - 点击"启动"按钮开始交易

6. 📈 监控策略
   - 查看策略日志了解运行状态
   - 监控持仓和盈亏情况
   - 观察信号生成和执行情况

⚠️ 注意事项:
- 首次使用建议在模拟环境中测试
- 确保有足够的资金和风险承受能力
- 定期检查策略表现并调整参数
- 如遇问题请查看策略日志
"""
    
    print(guide)
    
    # 保存使用指南到文件
    guide_file = Path(__file__).parent / "VNPY_USAGE_GUIDE.md"
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write("# VNPY Signal System策略使用指南\n\n")
        f.write(guide)
    
    print(f"✅ 使用指南已保存到: {guide_file}")

def main():
    """主测试函数"""
    print("🚀 VNPY策略注册和发现测试")
    print(f"测试时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行测试
    test1 = test_strategy_import()
    test2 = test_specific_strategy_import()
    test3 = test_strategy_instantiation()
    test4 = test_vnpy_discovery()
    
    # 创建使用指南
    create_vnpy_usage_guide()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    tests = [test1, test2, test3, test4]
    success_count = sum(tests)
    total_tests = len(tests)
    
    print(f"测试通过率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count >= 3:
        print("🎉 策略已成功注册到VNPY，可以在VeighNa Studio中使用！")
        print("📖 请查看生成的使用指南了解详细步骤")
    else:
        print("❌ 策略注册存在问题，请检查错误信息")
    
    print(f"\n测试完成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
