#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
超简化策略 - 绕过numpy兼容性问题
完全不依赖talib，纯Python实现
"""

from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
from vnpy.trader.constant import Direction, Offset, Status

class UltraSimpleStrategy(CtaTemplate):
    """超简化策略 - 绕过环境问题"""
    
    author = "VNPY Simple Strategy"
    
    # 策略参数
    fast_window = 10        # 快速均线周期
    slow_window = 20        # 慢速均线周期
    position_size = 1       # 仓位大小
    
    # 策略变量
    fast_ma = 0.0
    slow_ma = 0.0
    ma_trend = 0
    
    parameters = ["fast_window", "slow_window", "position_size"]
    variables = ["fast_ma", "slow_ma", "ma_trend"]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 使用最基础的组件
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager()
        
        # 手动维护价格历史
        self.price_history = []
        
    def on_init(self):
        """策略初始化"""
        self.write_log("超简化策略初始化")
        self.load_bar(30)
        
    def on_start(self):
        """策略启动"""
        self.write_log("超简化策略启动")
        self.trading = True
        
    def on_stop(self):
        """策略停止"""
        self.write_log("超简化策略停止")
        self.trading = False
        
    def on_tick(self, tick: TickData):
        """处理tick数据"""
        self.bg.update_tick(tick)
        
    def on_bar(self, bar: BarData):
        """处理bar数据"""
        self.bg.update_bar(bar)
        
    def on_1min_bar(self, bar: BarData):
        """处理1分钟K线"""
        # 手动更新ArrayManager
        self.am.update_bar(bar)
        if not self.am.inited:
            return
            
        # 维护价格历史
        self.price_history.append(bar.close_price)
        if len(self.price_history) > 50:
            self.price_history.pop(0)
        
        # 手动计算移动平均线
        if len(self.price_history) >= self.slow_window:
            # 快速移动平均
            self.fast_ma = sum(self.price_history[-self.fast_window:]) / self.fast_window
            # 慢速移动平均
            self.slow_ma = sum(self.price_history[-self.slow_window:]) / self.slow_window
            
            # 判断趋势
            if self.fast_ma > self.slow_ma:
                ma_trend = 1
            elif self.fast_ma < self.slow_ma:
                ma_trend = -1
            else:
                ma_trend = 0
            
            # 交叉信号
            if self.ma_trend != ma_trend:
                self.ma_trend = ma_trend
                
                # 金叉买入
                if ma_trend == 1 and self.pos == 0:
                    self.buy(bar.close_price, self.position_size)
                    self.write_log(f"金叉买入: {bar.close_price}")
                
                # 死叉卖出
                elif ma_trend == -1 and self.pos == 0:
                    self.short(bar.close_price, self.position_size)
                    self.write_log(f"死叉卖出: {bar.close_price}")
                
                # 平仓
                elif ma_trend != 0 and self.pos != 0:
                    if self.pos > 0:
                        self.sell(bar.close_price, abs(self.pos))
                        self.write_log(f"平多仓: {bar.close_price}")
                    else:
                        self.cover(bar.close_price, abs(self.pos))
                        self.write_log(f"平空仓: {bar.close_price}")
        
        self.put_event()
    
    def on_trade(self, trade: TradeData):
        """处理成交回报"""
        self.put_event()
    
    def on_order(self, order: OrderData):
        """处理委托回报"""
        pass
    
    def on_stop_order(self, stop_order: StopOrder):
        """处理停止单回报"""
        pass
